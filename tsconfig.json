{"compilerOptions": {"experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "outDir": "./dist", "lib": ["ES2019"], "module": "commonjs", "target": "ES2019", "sourceMap": true, "resolveJsonModule": true, "strictNullChecks": true, "declaration": true}, "exclude": ["node_modules", ".idea", "built", ".git", "coverage", ".nyc_output"], "include": ["./src/**/*", "index.ts"], "ts-node": {"transpileOnly": false, "files": true}, "typedocOptions": {"entryPoints": ["src/index.ts"], "out": "autoDocs"}}