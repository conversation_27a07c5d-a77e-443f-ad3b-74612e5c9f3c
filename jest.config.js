/** @type {import('ts-jest/dist/types').InitialOptionsTsJest} */
module.exports = {
    preset: 'ts-jest',
    testEnvironment: 'node',
    silent: false,
    testPathIgnorePatterns: ['.d.(ts|js)$', '.helper.(ts|js)$','dist/src/__tests__/helpers.js', 'dist/src/__tests__/setup.js', 'dist/src/__tests__/utils/'],
    reporters: ['jest-standard-reporter', 'jest-skipped-reporter', 'jest-failure-reporter','jest-junit', ],
    coverageReporters: ['json', 'text'],
    setupFiles: ['<rootDir>/dist/src/__tests__/setup.js']
};
