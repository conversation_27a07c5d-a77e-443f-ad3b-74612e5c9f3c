<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="All Unit Tests" type="JavaScriptTestRunnerJest">
    <node-interpreter value="project" />
    <jest-package value="$PROJECT_DIR$/node_modules/jest" />
    <working-dir value="$PROJECT_DIR$" />
    <jest-options value="--detectOpenHandles" />
    <envs />
    <scope-kind value="DIRECTORY" />
    <test-directory value="$PROJECT_DIR$/src/__tests__/unittest" />
    <method v="2" />
  </configuration>
</component>