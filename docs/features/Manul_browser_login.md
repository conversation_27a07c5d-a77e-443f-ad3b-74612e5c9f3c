# Intro

One of the means users can use to log in to a source is the manual browser login.
It should open a browser window in the app using the scraperLib (note that this window is NOT handled by scraperLib) and
allow the user to log in to the specified url.

## Potential risks

### Different account login

Manual browser login allows users to log in with a set of credentials `A` and once those expire use a different set `B`
to log in.
The problem that might occur is that `A` and `B` might have different privilege levels and thus lead to downloading
partially or in extreme cases completely different data sets.

That is why we want to make sure that if a user is using the manual login feature for a scraper, and he wants to update
the SourceAccount with a new manual login we can detect that the new account is different from the old one and not allow
him to update it.

We should allow him to make a new account and use it to scrape data if he wants to, but it needs to be a conscious
decision.
