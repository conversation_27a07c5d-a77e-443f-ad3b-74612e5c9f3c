# Provide manual login data for a specific platform

Because we want the process to be as seamless to the user as possible we will focus on embedding the manual login window inside the ElectronApp. The ScraperLib should provide the necessary data to obtain the user session but should NOT be responsible for spawning any visible browsers and obtaining the user session.

We want to be as efficient and seamless so if we can automate detecting the moment in which a user is logged in lets do that.
