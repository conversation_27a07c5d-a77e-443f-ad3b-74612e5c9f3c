# Intro

Stop/Kill the specific scraping process

# Scraping termination

The user has to have the ability to terminate any/all of them at any point.
The process should be terminated immediately with as little wait time as possible (none would be preferred).

# Scraping 'Stopping'

A stop feature allowing the user to finish up the currently running scraping and only then terminating ( or rather not
starting a new scraping process) was discussed
but was deemed unnecessary.
The reasoning being the most common use case for stopping scraping was freeing up resources on the users' machine to be
able to perform different work activities.
With that in mind the users want to free up their resources as soon as possible rather than to wait for the running
scraping to finish.
