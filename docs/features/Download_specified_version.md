# Download specified versions of scrapers & chromium based on user configuration

A single user can have up to X different scraper binaries (and their corresponding chromium downloads), where each binary can be used for a different scraper (worst case scenario X = number of supported sources). Before each use (additionally periodically?) the ScraperLib should check what version of scrapers should the user use and update them if necessary.

All binaries need to have an assigned puppeteer version (since puppeteer versions are strictly attached to specific chromium versions) but if multiple binaries require the same chromium version we do not want to download it multiple times.

These download processes would be best done behind the scenes/in a unified way? So that if someone starts downloading 3 STEAM scrapers the chromium update does not download chromium 3 times (concurrent race scenario).

ScraperLib should regularly check if there are any unused scrapers/chromium versions and dispose of them.
