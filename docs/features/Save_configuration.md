# Intro

There are two distinct values that can be called "configuration" `secrets` and `open settings`.
Both of them can (and eventually will) be treated separately in different
ways and should be clearly and easily distinguishable.

There are also two different modes a configuration can be used `local` and `remote`

# Configuration distinctions

## Types of configuration

### Secrets

secrets contain all the "secret" parts of a configuration. Things that can NOT be kept in open text in our DB.
For example:

* logins
* passwords
* cookies
* totp secrets
* etc.

**Neither devs nor any IndieBI personnel should be able to check direct values of secrets!**

### Open settings

open settings contain all the visible parts of configurations.
For example:

* scheduled scraping hour
* ignored sku id's
* ignored source side organizations
* etc.

This information while not publicly available to all IndieBI users should be manageable by IndieBI team members to allow
for easier customer support.

### Non configuration options

It is worth noting that while scraper versions are configurable per user this information is NOT configured via the user
configuration mechanism.

## Types of configuration storage

We do not want to duplicate any configuration storage means that might be available in the app that is using the
scraperLib so our configuration storage should be hidden
behind an easy-to-use interface that abstracts the 'what to save in what way'
issues and just requires the using devs to provide the implementation of 'how to save'.

### local

As the name implies all configuration values should be kept locally on the users machine.
If any local/client specific overrides are necessary the means for them should be provided by the using app developers.

### Remote

All user configuration options are kept on IndieBI secure servers (this is the only way that allows for users for
configuration sharing).

The specifics of this configuration mode will be fleshed out later on.
The current "general idea" is to use a`secure cloud server with user hash encryption`. 
