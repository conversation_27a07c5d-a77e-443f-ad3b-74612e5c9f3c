# Intro

Each user is encouraged (turned on by default) to use the scheduled scraping feature.
Scheduled runs are the main way our system obtains new data for the user. It is a crucial component.

# Run conditions

Scheduled scraping should run a single source at a time rather than few sources in parallel to minimize the usage of the
users' system resources.

# Configuration

The user should be able to specify when the scheduled scrape should happen (using his local time).
If not specified then the default hour (using users local time, not UTC) should be used.

Users should be able to switch scheduled scraping off, but it is a move that we want to discourage.
When using remote storage we should be able to look up and modify when the scheduled scrape is set to run to allow for
even system load distribution.   
