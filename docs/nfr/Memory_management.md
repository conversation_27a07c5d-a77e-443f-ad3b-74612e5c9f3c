We want to be able to run multiple scraping processes. Since all of them are isolated and use their own chromium instance it is easy to blow up user memory usages. We need to keep in mind that there are some of our users that work on a 8GB business laptop and run more than just Scrapers.

The memory usage should be measured and monitored we want to keep a single user scraper below X MB of memory (NEED TO MEASURE HOW MUCH WE ARE USING NOW)
