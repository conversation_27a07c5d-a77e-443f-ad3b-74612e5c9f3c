# Overview

There are two similar but not identical concepts `run` and `execution`

When we are talking about running a scraper we are talking about `run`.

## Description and relation

A `run` can be described as the below process:

- User clicking to run a scraper (or a scheduled scrape starting).
- ScraperLib requesting date range gaps for configured sources.
- ScraperLib checking if a new version of the scraper is available.
- ScraperLib run validateSession
- If session is invalid AND login data is available try to refresh session
- If refresh was a success for each date range gap a scraper binary is then used to download it. **This step is
  the `execution`**.
- Each downloaded report is uploaded to the scraper-API.

In summary a single `run` can consist of multiple `execution` steps.

## Additional run data

Each `run` should return 2 additional numbers:

- the amount of days that were downloaded in it (Downloaded 400 days of data)
- the amount of days that we were unable to download (without specific reasons) (I had a problem with 20 days of data).

Why is this data needed?

A user starts his steam_sales run and needs to download 5 date ranges.
Three of those date ranges were downloaded successfully and data was parsed, the other two failed to scrape.
Is this run a failure? - data was downloaded and processed correctly
Is it a success? - scraping operations failed and data was not processed
By stating how many days we were able to scrape we can give the user an indication of progress being made even in the
case of errors.

## Main run failure reasons

- Failure to communicate with Scraper-API
- Failure of an `execution`.
- Failure to upload a downloaded file will also result in failing an entire run.
  No 'in between' state should be held, if we are unable to upload the file (with retries) it should be deleted and the
  entire run marked as failed.
- Other.
