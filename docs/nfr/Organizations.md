# Intro

In the old 'Console app' and backend there was no separation between a User and a organization (sometimes also called a
studio).
We want to introduce such a separation level to increase the ease of data scraping/sharing/management.

# Limitations

Organizations are not to be created locally, users do not have the possibility to create new organizations.
They will be created in a completely different service.
The only exception to this is creating a local organization for the first time while
using [local storage settings](/docs/features/Save_configuration.md)

When migrating to the external storage we should migrate the user private configuration and clear the local storage ( To
be verified in development).

_Use case_

- User starts with local storage.
- Sets up his scrapers.
- The app creates a default account for the user to use locally.
- User Switches to remote storage - there should be a migration of his local configuration to the remote one
