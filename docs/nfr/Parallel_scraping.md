# Parallel scraping

In the original concept parallel scraping of a single source was introduced (example: download Steam sales with 3
parallel processes).

After investigating we came to the conclusion that the main drive behind such parallel scraping is speeding up the
process in which data scraped by the user is available to him on our dashboards.

This **can be achieved with simpler steps** like:

- **Inverting the current download order** so that we download the latest data first and move back in date time
  downloading
  older data
- **Migrating the scrapers to BrowserV2** to introduce a shift in waiting approach

That along with the issues we encountered while trying to prepare wireframes for the process lead us to the conclusion
below.

The only parallel downloads that we will want to implement are downloads of different sources:
Example:

**As a user I want to be able to download steam sales and microsoft sales at the same time.**
