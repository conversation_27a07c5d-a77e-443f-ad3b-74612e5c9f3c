# Communicate with Scraper-API

Since Scraper binaries should limit their communication with Scraper-API the majority of that communication should be handled by ScraperLib.

## Obtaining dates

ScraperLib is mainly responsible for obtaining the date ranges from the Scraper-Api and running scrapers with those date ranges.
Any time machinations should be made on the Scraper-Api the ScraperLib is only a recipient of those ranges.

## Uploading files

When a scraper finishes its run the end result is a zip file containing reports. This file should be uploaded by the Scraper-API to the Report Service.
