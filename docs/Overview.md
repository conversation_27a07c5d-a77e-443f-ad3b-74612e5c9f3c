# Design assumptions

-   ScraperLib should be framework-agnostic, it should be usable in both Electron apps and server side scraping workers (if those are ever created).
-   The provided API should be user-centric. All common use cases should be provided as specific functions by the API.
-   Scrapers should be run as separate processes.
    Currently, all scrapers are implemented in Node.js but that might not be the case in the future. ScraperLib should be designed and implemented in a way that allows running scrapers written in different technologies (Python?).

## Support for older node

ScraperLib will be used in electron-app. It means that runtime node version can be lower that development node. It can cause many problems when using native packages.

Historical background:
To prevent this we added `"@types/node": "12.11.0"` to devDependencies and set `node:12.16.3` as image for CI/CD tests.
We shouldn't update `@types/node` and docker image for tests or it should be a conscious decision to drop support for older node.
Currently lowest supported version is `node:12.16.3` (main thread runtime for `"electron": "10.4.3"`)

Some known problems (typescript with `"@types/node": "12.11.0"` treat them as error):
`fs` - We can't use `fs/promises`
`stream` - We can't use `stream/promises`
`crypto` - We can't use `randomUUID`, we have `uuid` package instead
`child_process` - Some interfaces were added in later versions of node. If you have doubts check [documentation](https://nodejs.org/docs/latest-v12.x/api/child_process.html#child_processspawncommand-args-options)

On 10.2024 the Electron node was updated to 18.18.12 so most of the above problems should be solved.

## Process relation

![image](img/ElectronScraperProcessRelation..png)

## Communication flow

![image](img/ElectronScraperCommunicationFlow.png)

## Known package update problems

We use `unzipper` for our unzip operations. 
It has a problem with its latest versions (0.12.2 and latest 0.12.3).
It causes a problem with Electron as one of its dependencies (aws-s3 related) is stated as a dev dependency in the package,
but is used in code as a non dev dependency.
It causes a problem with the Electron build process (vite). 

As a short term solution I downgraded the package to 0.12.1 as the last known working version.
As a long term solution we should change packages! 
Note: there is an option to skip the error in the Electron builder, but I did not try it since it seems a bad idea to hide the error.

## Renderer safe
