build:
    cache:
        key:
            files:
                - package-lock.json
        paths:
            - .npm/
    variables:
        PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: 1
    stage: build
    # build and test jobs are merged to minimize time spent
    # on switching jobs and uploading/downloading intermediate artifacts.
    script:
        # build
        - npm config set '//gitlab.com/api/v4/packages/npm/:_authToken' "${CI_JOB_TOKEN}"
        - time npm ci --cache .npm --prefer-offline --no-audit --no-fund
        - time npm run build
    artifacts:
        when: on_success
        paths:
            - node_modules
            - dist
