.tests-base: &tests-base
    needs: ['build']
    stage: tests
    artifacts:
        paths:
            - coverage/
        when: always
        reports:
            junit: junit.xml

unit tests:
    <<: *tests-base
    script:
        - ./scripts/encrypt_credentials.sh --decrypt
        - npm run test-unit-ci
        - cp ./coverage/unit/coverage-final.json ./coverage/unit.json

integration tests:
    <<: *tests-base
    script:
        - ./scripts/encrypt_credentials.sh --decrypt
        - npm run test-integration-ci
        - cp ./coverage/integration/coverage-final.json ./coverage/integration.json

acceptance tests:
    <<: *tests-base
    tags:
        - dpt-azure
    allow_failure: false
    before_script:
        - mkdir -p /builds/bluebrick/indiebi/scraper-team/scraperlib/.private/binaries/chromium/chromium-linux-$CHROMIUM_REVISION/
        - cp -R /root/.chromium/linux-$CHROMIUM_REVISION/chrome-linux/ /builds/bluebrick/indiebi/scraper-team/scraperlib/.private/binaries/chromium/chromium-linux-$CHROMIUM_REVISION
    script:
        - ./scripts/encrypt_credentials.sh --decrypt
        - npm run test-acceptance-ci
        - cp ./coverage/acceptance/coverage-final.json ./coverage/acceptance.json

report coverage:
    stage: tests
    needs: ['acceptance tests', 'integration tests', 'unit tests']
    coverage: /All files[^|]*\|[^|]*\s+([\d\.]+)/
    script:
        - npx nyc@15 report -t coverage --report-dir coverage --reporter=text

chromium version check:
    needs: []
    stage: tests
    allow_failure: true
    script:
        - |
            REQUIRED_CHROMIUM_VERSION=`curl -fsL https://scraper-api.indiebi.com/1/latest-stable-edition | jq ".chromiumRevision"`
            if [ "$CHROMIUM_REVISION" != "$REQUIRED_CHROMIUM_VERSION" ]; then
                echo "Tests are running slower than possible, because prepared Chromium version in scraperlib-ci-cd-image docker image is not up to date. Expected: $REQUIRED_CHROMIUM_VERSION, Actual: $CHROMIUM_REVISION."
                echo "To speed up tests, update CHROMIUM_REVISION in https://gitlab.com/bluebrick/indiebi/scraper-team/scraperlib-ci-cd-image/-/blob/main/Dockerfile"
                exit 1
            fi

lint:
    needs: ['build']
    stage: tests
    script:
        - npm run eslint

circular dependency check:
    needs: ['build']
    stage: tests
    script:
        - npm run check-circular-dependencies

detect open handles:
    <<: *tests-base
    script:
        - ./scripts/encrypt_credentials.sh --decrypt
        - npm run test-ci -- --detectOpenHandles --config jest.ci.config.js
    rules:
        - if: $CI_PIPELINE_SOURCE == "schedule"
