{
  "[json]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[markdown]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[typescript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "jest.runMode": {
    "type": "on-demand",
  },
  "jest.jestCommandLine": "node_modules/.bin/jest --testTimeout=100000000",
  "diffEditor.ignoreTrimWhitespace": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.formatOnSave": true,
  "files.eol": "\n",
  "testExplorer.useNativeTesting": true,
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "typescript.implementationsCodeLens.enabled": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.referencesCodeLens.enabled": true,
  "typescript.referencesCodeLens.showOnAllFunctions": false,
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": false
}