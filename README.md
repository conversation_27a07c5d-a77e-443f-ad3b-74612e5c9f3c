<!--- docs
# Metadata used by our doc generator
title: scraperLib
group: scrapers
-->

# Intro

To work on this component you need to understand the Design and development approach.
Please read them and understand them "**by heart**" rather than simply "being aware of their existence".

Check the [docs'](./docs) folder for any design specific information

## Design

Information about the requirements is gathered in two confluence documents (and their sub documents):

1. User stories document: https://indiebi.atlassian.net/wiki/spaces/INDIEBI/pages/361721
2. ScraperLib behaviors document: https://indiebi.atlassian.net/wiki/spaces/INDIEBI/pages/362121/ScraperLib+behaviors

Each one describes the general look and feel of the scraperLib

## Development approach

We want to keep a customer driven and TDD approach, before implementing a feature it should be identified
(prototyped if need be) understood and described form a user perspective.

Once identified prepare a test that tests the behaviour and implement it so that it fails (red/green testing).

# Getting started

Once you read through the intro section ...

# Development helpers

To help working/debugging ScraperLib we added few "feature flags" in code. They are based on environment variables (it's up to you how to pass them to application)

-   `FORCED_SCRAPER_VERSION` & `FORCED_CHROMIUM_REVISION` - force scraperLib to use specific version of scrapers binary and chromium. Useful when you want to use new wip version or lse contains a bug. To use forced versions both variables have to be defined!
-   `DEBUG` - if `true` scraper binary will work i


# How to develop in electron-app
You can build a tar file and install it in electron-app

```shell
npm pack
```

Copy the file path and install it in electron-app:

```shell
npm install <path-to-tar-file>
```
