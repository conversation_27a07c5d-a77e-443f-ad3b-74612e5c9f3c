#!/bin/bash
set -o errexit

# This scripts installs locally built scraperlib into local version of electron-app, presumably located next to scraperlib directory.
# To avoid problem with npm caching, firsts changes the version of scraperlib in package.json to a `0.0.<isodate-like-format>`.
# It uses `npm pack --pack-destination ./` to create a tarball of scraperlib
# Edits package.json of electron-app, and replaces current version of "@bluebrick/scraperlib": package with a local file reference, like this:
#    "@bluebrick/scraperlib": "file:<full-path>/scraperlib/bluebrick-scraperlib-0.0.<SCRAPERLIB_VERSION>.tgz"
# Then it runs `npm install` in electron-app directory. At the end it then restores the original version of scraperlib in package.json.

SCRAPERLIB_VERSION=$(date +"%Y%m%d%H%M%S")

build_scraperlib_tarball() {
    echo "Removing old tarballs..."
    rm -f bluebrick-scraperlib-*.tgz
    echo "Building scraperlib tarball..."
    sed -E 's/"version": "[0-9]+\.[0-9]+\.[0-9]+"/"version": "0.0.'$SCRAPERLIB_VERSION'"/' package.json > package.json.tmp && mv package.json.tmp package.json
    npm pack --pack-destination ./
    sed -E 's/"version": "[0-9]+\.[0-9]+\.[0-9]+"/"version": "0.0.0"/' package.json > package.json.tmp && mv package.json.tmp package.json

}

install_scraperlib() {
    echo "Installing scraperlib version $SCRAPERLIB_VERSION"
    cd ../electron-app/
    SCRAPERLIB_TARBALL=$(realpath ../scraperlib/bluebrick-scraperlib-0.0.$SCRAPERLIB_VERSION.tgz)
    if [ -f ~/.nvm/nvm.sh ]; then
            source ~/.nvm/nvm.sh
            nvm use
    fi
    npm install $SCRAPERLIB_TARBALL
    cd ../scraperlib
}

build_scraperlib_tarball
install_scraperlib

