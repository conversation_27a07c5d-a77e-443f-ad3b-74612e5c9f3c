#!/bin/bash

decrypted_file=.env.test
encrypted_file=.env.test.encrypted

if [ -z "$SCP_CREDENTIALS_ENCRYPTION_KEY" ]; then
    echo "SCP_CREDENTIALS_ENCRYPTION_KEY is not set"
    exit 1
fi

passphrase=$SCP_CREDENTIALS_ENCRYPTION_KEY

if [ "$1" == "--encrypt" ]; then
    gpg  -o- --symmetric --passphrase $passphrase --batch --yes --cipher-algo AES256 $decrypted_file | base64 > $encrypted_file
elif [ "$1" == "--decrypt" ]; then
    base64 --decode < $encrypted_file | gpg --output $decrypted_file --passphrase $passphrase --batch --yes --decrypt
else
    echo "No command given. Usage: ./encrypt_credentials.sh --encrypt|--decrypt"
    exit 1
fi
