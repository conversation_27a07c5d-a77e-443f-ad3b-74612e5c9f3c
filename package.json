{"name": "@bluebrick/scraperlib", "version": "0.0.0", "description": "", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "engines": {"node": "18.18.2", "npm": ">=8.5.5"}, "scripts": {"eslint": "eslint \"src/**/*.ts\" --max-warnings 0 --ext .js,.ts --cache", "eslintFix": "eslint \"src/**/*.ts\" --fix --ext .js,.ts", "lint": "npm run eslint", "test": "npm run test-unit", "test-unit": "jest --config jest.ts.config.js src/__tests__/unittest --resetMocks", "test-integration": "jest --config jest.ts.config.js /src/__tests__/integration --resetMocks", "test-acceptance": "jest --config jest.ts.config.js /src/__tests__/acceptance*  --runInBand", "test-ci": "jest dist/src/__tests__/ --ci --coverage --color --runInBand --resetMocks", "test-unit-ci": "jest dist/src/__tests__/unittest --ci --coverage --color --resetMocks --coverageDirectory='./coverage/unit'", "test-integration-ci": "jest dist/src/__tests__/integration --ci --coverage --color --resetMocks --coverageDirectory='./coverage/integration'", "test-acceptance-ci": "jest dist/src/__tests__/acceptance* --ci --coverage --color --coverageDirectory='./coverage/acceptance' --runInBand", "typedoc": "typedoc", "tscwatch": "tsc --watch", "jestwatch": "jest --watchAll", "build": "rm -Rf dist && tsc --declaration", "installHusky": "husky install", "prepare": "husky install && chmod +x .husky/pre-* && ./scripts/encrypt_credentials.sh --decrypt", "prepack": "npm run build", "check-circular-dependencies": "madge --circular --extensions ts src"}, "repository": {"type": "git", "url": "**************:bluebrick/indiebi/scraper-team/scraperlib.git"}, "publishConfig": {"@bluebrick:registry": "https://gitlab.com/api/v4/projects/35626588/packages/npm/"}, "author": "SCP team", "dependencies": {"@sentry/node": "9.10.1", "axios": "0.30.0", "chmodr": "1.2.0", "class-transformer": "0.5.1", "class-transformer-validator": "0.9.1", "class-validator": "0.14.0", "cron": "2.1.0", "cryptr": "4.0.2", "json-stringify-safe": "5.0.1", "reflect-metadata": "0.1.13", "tree-kill": "1.2.2", "unzipper": "0.12.3", "uuid": "8.3.2"}, "devDependencies": {"@bluebrick/eslint-config-scp": "2.0.1", "@types/cron": "2.0.0", "@types/jest": "28.1.3", "@types/mock-fs": "4.13.4", "@types/node": "16.11.7", "@types/uuid": "8.3.0", "@typescript-eslint/eslint-plugin": "5.26.0", "dotenv": "16.0.1", "husky": "8.0.1", "jest": "28.0.1", "jest-failure-reporter": "1.0.1", "jest-junit": "14.0.0", "jest-mock-axios": "4.6.1", "jest-mock-process": "2.0.0", "jest-skipped-reporter": "0.0.5", "jest-standard-reporter": "2.0.0", "lint-staged": "14.0.1", "lodash": "4.17.21", "madge": "^6.0.0", "mock-fs": "5.5.0", "prettier": "2.6.2", "ts-jest": "28.0.5", "typedoc": "0.22.15", "typescript": "4.6.3"}, "lint-staged": {"src/**/*.{js,ts,json}": "eslint --cache --fix"}}