import {exec} from 'child_process';
import {promises as fs} from 'fs';
import * as path from 'path';
import {promisify} from 'util';
import {getDependenciesInfo} from '../api/ScraperApi';
import {errorType} from '../configurations/errorType';
import {ScraperLibError} from '../processes/types/errors';
import {ScraperServiceLogLevel} from '../telemetry/scraperService/iScraperServiceClient';
import * as telemetry from '../telemetry/telemetry';
import {ShadowModeTask} from '../types';
import {Source} from '../types/Source';
import {filterItemsFromArray} from '../utils/arrayUtils';
import {promiseState} from '../utils/asyncUtils';
import {downloadFile, moveFile, unZipFileAndDelete} from '../utils/fileUtils';
import * as fileUtils from '../utils/fileUtils';
import {executableName, getPlatform, isMac} from '../utils/platformUtils';
import {retryAction} from '../utils/retryAction';
import {getChromiumId, getScraperId} from './identifiers';
import {Dependency, DependencyDetails, DependencyType, SourceDependencies} from './types';

/***************************************************************************************************************
 *                                                                                                             *
 *      DEPENDENCIES MANAGER DIRECTORIES NAMING CONVENTION, by examples:                                       *
 *                                                                                                             *
 *      <-----mainDir-------------------->                                                                     *
 *      <-----binDirectory------------------------>/<-type*>                                                   *
 *      <-----dependencyTypeDirectory---------------------->/<---------id------------>                         *
 *      <-----dependencyRootDirectory------------------------------------------------>                         *
 *      <-----dependencyExecPath ----------------------------------------------------------->                  *
 *      ~/.config/indiebi-desktop/scrapers/binaries/scrapers/scrapers-cli-linux-0.52.2/scrape                  *
 *                                                                                                             *
 *      <-----mainDir-------------------->                                                                     *
 *      <-----binDirectory------------------------>/<-type*>                                                   *
 *      <-----dependencyTypeDirectory---------------------->/<--------id--------->                             *
 *      <-----dependencyRootDirectory-------------------------------------------->                             *
 *      <-----dependencyExecPath -------------------------------------------------------------------->         *
 *      ~/.config/indiebi-desktop/scrapers/binaries/chromium/chromium-linux-970485/chrome-linux/chrome         *
 *                                                                                                             *
 **************************************************************************************************************/

const dependencyTypes = Object.values(DependencyType);

const areDependenciesEqual = (dep1: Dependency, dep2: Dependency) => dep1.id === dep2.id && dep1.type === dep2.type;

const {platform} = getPlatform();

function prepareUniqueDependencyList(dependencies: DependencyDetails[]): DependencyDetails[] {
    const flatDependencies: DependencyDetails[] = [];

    function addDependencyIfNotExist({dependencies: subDependencies, ...dependency}: DependencyDetails): void {
        if (!flatDependencies.some((dep) => dep.id === dependency.id)) {
            flatDependencies.push(dependency);
        }
        subDependencies?.forEach(addDependencyIfNotExist);
    }

    dependencies.forEach(addDependencyIfNotExist);
    return flatDependencies;
}

export class DependenciesManager {
    private syncProcess?: Promise<void>;
    private readonly binDirectory: string;

    /**
     * @description Returns directory for given DependencyType: Examples:
     * - ./binaries/chromium/
     * - ./binaries/scrapers/
     */

    private getDependencyTypeDirectory = (type: DependencyType): string => path.join(this.binDirectory, type);

    /**
     * @description Returns rootDirectory for given Dependency: Examples:
     * - ./binaries/chromium/chromium-linux-970485/
     * - ./binaries/scrapers/scrapers-cli-linux-0.52.2/
     */
    private getDependencyRootDirectory = ({type, id}: Dependency): string => path.join(this.getDependencyTypeDirectory(type), id);

    /**
     * @description Returns execPath for given Dependency: Examples:
     * - ./binaries/chromium/chromium-linux-970485/chrome-linux/chrome
     * - ./binaries/scrapers/scrapers-cli-linux-0.52.2/scrape
     */
    getDependencyExecPath = ({type, id}: Dependency): string => {
        switch (type) {
            case DependencyType.CHROMIUM: {
                const innerPath = isMac() ? 'Chromium.app/Contents/MacOS' : '';
                const execName = isMac() ? 'Chromium' : executableName('chrome');
                return path.join(
                    this.getDependencyRootDirectory({
                        type,
                        id
                    }),
                    `chrome-${platform}`,
                    innerPath,
                    execName
                );
            }
            case DependencyType.SCRAPERS:
                return path.join(this.getDependencyRootDirectory({type, id}), executableName('scrape'));
            default:
                throw new Error(`Unknown dependency type: ${type}`);
        }
    };

    async getDependencyExecPaths(source: Source): Promise<{chromiumExecPath: string; scrapersExecPath: string}> {
        const scrapers = await DependenciesManager.getDependency(source);
        const chromium = scrapers.dependencies?.find((dep) => dep.type === DependencyType.CHROMIUM);
        if (!chromium) {
            throw new Error('Chromium dependency not found');
        }

        return {
            chromiumExecPath: this.getDependencyExecPath(chromium),
            scrapersExecPath: this.getDependencyExecPath(scrapers)
        };
    }

    getExecPathsForShadowRun(shadowModeTask: ShadowModeTask): {forcedChromiumExecPath: string; forcedScrapersExecPath: string} {
        return {
            forcedChromiumExecPath: this.getDependencyExecPath({type: DependencyType.CHROMIUM, id: getChromiumId(shadowModeTask.chromium)}),
            forcedScrapersExecPath: this.getDependencyExecPath({type: DependencyType.SCRAPERS, id: getScraperId(shadowModeTask.scrapers)})
        };
    }

    public static sourceDependencies: SourceDependencies;

    constructor(mainDir: string) {
        this.binDirectory = path.join(mainDir, 'binaries');
    }

    static async getDependency(source: Source): Promise<DependencyDetails> {
        if (!DependenciesManager.sourceDependencies) {
            DependenciesManager.sourceDependencies = await getDependenciesInfo();
        }
        return DependenciesManager.sourceDependencies[source] ?? DependenciesManager.sourceDependencies.default;
    }

    async movePreProvidedChromium(pathToDirContainingChromium: string): Promise<void> {
        const pathToChromium = await this.lookForPreProvidedChromium(pathToDirContainingChromium);
        if (pathToChromium == null) {
            return;
        }
        const localVersions = await this.getAvailableLocalVersions();
        const base = path.parse(pathToChromium).base;
        const isLocalVersionAvailable = localVersions.find((dependency) => dependency.type === DependencyType.CHROMIUM && dependency.id === base);
        if (isLocalVersionAvailable) {
            return;
        }

        const rootDir = this.getDependencyRootDirectory({type: DependencyType.CHROMIUM, id: base});
        await moveFile(pathToChromium, rootDir);
    }

    private async lookForPreProvidedChromium(pathToChromium: string): Promise<string | null> {
        try {
            const files = await fs.readdir(pathToChromium);
            const matchingFiles = files.filter((file) => /chromium-.*/.test(file));

            if (matchingFiles.length < 1) {
                telemetry.trace(`No Chromium binary found in the provided directory: ${pathToChromium}`, {logLevel: ScraperServiceLogLevel.WARN});
                return Promise.resolve(null);
            } else if (matchingFiles.length > 1) {
                telemetry.trace('More than one Chromium binary found locally. Proceeding with the first binary discovered.', {logLevel: ScraperServiceLogLevel.WARN});
            }

            const fullPath = path.join(pathToChromium, matchingFiles[0]);
            telemetry.trace(`Discovered preprovided Chromium ${fullPath}`, {logLevel: ScraperServiceLogLevel.INFO});
            return Promise.resolve(fullPath);
        } catch (err) {
            telemetry.trace(`Could not locate Chromium in the directory: ${pathToChromium}, message: ${err.message}`, {logLevel: ScraperServiceLogLevel.WARN});
            return Promise.resolve(null);
        }
    }

    private createSyncProcess(skipShadowBinaries: boolean): Promise<void> {
        return new Promise<void>(async (resolve, reject) => {
            try {
                await this.removeOldDependencies();
                await this.checkAndRemoveBrokenDependencies();
                const missingDependencies = await this.getMissingDependenciesList();
                if (!missingDependencies.length) {
                    telemetry.trace('All binaries are up to date');
                } else {
                    const dependenciesToDownload = skipShadowBinaries ? missingDependencies.filter(({shadowMode}) => !shadowMode) : missingDependencies;
                    await Promise.all(dependenciesToDownload.map((dependency) => this.downloadDependencyWithRetry(dependency)));
                }
                return resolve();
            } catch (error) {
                telemetry.exception(error, false, {operation: 'syncDependencies'});
                return reject(error);
            } finally {
                telemetry.trace('Syncing process finished');
                this.syncProcess = undefined;
            }
        });
    }

    private async getAvailableLocalVersions(): Promise<Dependency[]> {
        let dependenciesTypesDirs: string[];
        try {
            dependenciesTypesDirs = await fs.readdir(this.binDirectory);
        } catch (error) {
            telemetry.exception(error, false, {operation: 'getAvailableLocalVersions'});
            /*
            the errors that we can get here are either that the directory does not exist or a user has no access to it
            either way we do not have any good options for the user and do not want to block him so returning no available local versions seems ok
             */
            return [];
        }

        const foundVersions: Dependency[] = [];
        const types = dependencyTypes.filter((type) => dependenciesTypesDirs.includes(type));
        for (const type of types) {
            const directories = await fs.readdir(this.getDependencyTypeDirectory(type));
            foundVersions.push(...directories.map((dirName) => ({type, id: dirName})));
        }
        return foundVersions;
    }

    private async checkAndRemoveBrokenDependencies(): Promise<void> {
        const localVersions = await this.getAvailableLocalVersions();
        for (const dependency of localVersions) {
            if (!(await this.isDependencyWorking(dependency))) {
                await this.attemptRemoveDependency(dependency);
            }
        }
    }

    private async attemptRemoveDependency(dependency: Dependency, throwErrorOnFailRemoval = true): Promise<void> {
        const dependencyRootDir = this.getDependencyRootDirectory(dependency);
        await fileUtils.removeFileOrDirectory(dependencyRootDir, throwErrorOnFailRemoval);
    }

    // TODO this shouldn't be public
    async getMissingDependenciesList(): Promise<DependencyDetails[]> {
        const localVersions = await this.getAvailableLocalVersions();
        const requiredVersions = await this.getStudioDependencies();
        return filterItemsFromArray<DependencyDetails, Dependency>(requiredVersions, localVersions, areDependenciesEqual);
    }

    private async downloadDependencyWithRetry(dependency: DependencyDetails): Promise<void> {
        try {
            await this.downloadAndValidateDependency(dependency);
        } catch (error) {
            telemetry.exception(error, false, {operation: 'downloadDependencyWithRetry', dependency});
            telemetry.trace(`Failed to download dependency ${dependency.id}, retrying...`);
            await this.downloadAndValidateDependency(dependency);
        }
    }

    private async downloadAndValidateDependency(dependency: DependencyDetails): Promise<void> {
        await this.downloadDependency(dependency);
        if (!(await this.isDependencyWorking(dependency))) {
            await this.attemptRemoveDependency(dependency);
            throw new Error(`Downloaded binary ${dependency.id} is not valid`);
        }
    }

    private async getStudioDependencies(): Promise<DependencyDetails[]> {
        DependenciesManager.sourceDependencies = await getDependenciesInfo();
        telemetry.trace(`Studio dependencies, ${JSON.stringify(DependenciesManager.sourceDependencies)}`);
        return prepareUniqueDependencyList(Object.values(DependenciesManager.sourceDependencies));
    }

    async downloadDependency({type, id, downloadUrl, shadowMode}: DependencyDetails): Promise<void> {
        telemetry.trace(`Downloading ${type} ${id} ${shadowMode ? '(shadowMode)' : ''}...`);
        const dirPath = this.getDependencyTypeDirectory(type);
        const downloadedFilePath = await downloadFile(downloadUrl, dirPath, shadowMode);
        const rootDir = this.getDependencyRootDirectory({type, id});

        if (type === DependencyType.CHROMIUM && downloadedFilePath.endsWith('.zip')) {
            await unZipFileAndDelete(downloadedFilePath, rootDir);
        }
        if (type === DependencyType.SCRAPERS) {
            const targetFilePath = path.join(rootDir, executableName('scrape'));
            await moveFile(downloadedFilePath, targetFilePath);
        }
    }

    async isDependencyWorking(dependency: Dependency): Promise<boolean> {
        const execPath = this.getDependencyExecPath(dependency);

        telemetry.trace(`Testing if dependency is working: ${JSON.stringify(dependency)} at ${execPath}`);

        let command = `"${execPath}" --version`;

        if (dependency.type === DependencyType.CHROMIUM && platform === 'win') {
            command = `powershell -command "&{(Get-Item '${execPath}').VersionInfo.ProductVersion}"`;
        }

        try {
            return await retryAction({
                target: async (): Promise<boolean> => {
                    const {stderr, stdout} = await promisify(exec)(command);
                    telemetry.trace(`stderr: ${stderr}, stdout: ${stdout}`);
                    return !stderr && !!stdout;
                },
                delay: 3000,
                backoff: 1,
                maxAttempts: 3,
                label: 'validating dependency'
            });
        } catch (error) {
            telemetry.exception(error, false, {operation: 'isDependencyWorking', dependency});
            return false;
        }
    }

    private async removeOldDependencies(): Promise<void> {
        const localVersions = await this.getAvailableLocalVersions();
        const requiredVersions = await this.getStudioDependencies();
        const dependencies = filterItemsFromArray<Dependency>(localVersions, requiredVersions, areDependenciesEqual);
        await Promise.all(dependencies.map((dep) => this.attemptRemoveDependency(dep, false)));
    }

    /** This method should throw errors by default, but we added shouldThrowSyncException parameter for backward compatibility */
    public async syncDependencies(skipShadowBinaries = false, shouldThrowSyncException = false): Promise<void> {
        if (this.syncProcess ? (await promiseState(this.syncProcess)) != 'pending' : true) {
            telemetry.trace('Starting dependencies syncing process');
            this.syncProcess = this.createSyncProcess(skipShadowBinaries);
        } else {
            telemetry.trace('Another process of syncing in progress, skipping');
        }
        try {
            return await this.syncProcess;
        } catch (error) {
            telemetry.exception(error, false, {operation: 'syncDependencies'});
            if (shouldThrowSyncException) {
                throw new ScraperLibError(errorType.DEPENDENCIES_SYNC_ERROR);
            }
        }
    }
}
