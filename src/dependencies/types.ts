import {Source} from '../types';

export enum DependencyType {
    CHROMIUM = 'chromium',
    SCRAPERS = 'scrapers'
}

export interface Dependency {
    id: string;
    type: DependencyType;
}

export interface DependencyDetails extends Dependency {
    downloadUrl: string;
    dependencies?: DependencyDetails[];
    shadowMode?: boolean;
}

type ShadowModeKey = string;

export type SourceDependencies = Record<'default', DependencyDetails> & Partial<Record<Source, DependencyDetails>> & Record<ShadowModeKey, DependencyDetails>;
