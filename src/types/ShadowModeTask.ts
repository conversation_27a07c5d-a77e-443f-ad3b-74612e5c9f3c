import {Command} from './Command';
import {DateRange} from './DateRange';
import {Source} from './Source';

interface ShadowModeCommonTask {
    source: Source;
    command: Command.SCRAPE | Command.LOGIN;
    scrapers: string;
    chromium: number;
    id: string;
    label: string;
}

export interface ShadowModeScrapeTask extends ShadowModeCommonTask {
    command: Command.SCRAPE;
    dateRanges: DateRange[];
}

export interface ShadowModeLoginTask extends ShadowModeCommonTask {
    command: Command.LOGIN;
}

export type ShadowModeTask = ShadowModeScrapeTask | ShadowModeLoginTask;
