const scrapersEventTypes = {
    output: 'output',
    type: 'result',
    error: 'error',
    dualAuth: 'dualAuth'
} as const;

export const eventTypes = {
    generic: 'generic',
    progress: 'progress',
    initialized: 'initialized',
    scraperRunUpdate: 'scraper-run-update',
    dependencySync: 'dependency-sync',
    libError: 'lib-error', // should be used with messages from errorMessages.ts,
    ...scrapersEventTypes
} as const;

export type EventType = typeof eventTypes[keyof typeof eventTypes];
