import {Portal} from './Portal';
import {Source} from './Source';

const sourceToPortalMapping: Record<Source, Portal> = {
    [Source.MICROSOFT_SALES]: Portal.MICROSOFT,
    [Source.HUMBLE_SALES]: Portal.HUMBLE,
    [Source.GOG_SALES]: Portal.GOG,
    [Source.NINTENDO_SALES]: Portal.NINTENDO,
    [Source.NINTENDO_FREE_TO_PLAY]: Portal.NINTENDO,
    [Source.NINTENDO_PREORDERS]: Portal.NINTENDO,
    [Source.NINTENDO_WII_DS_SALES]: Portal.NINTENDO,
    [Source.NINTENDO_WISHLISTS]: Portal.NINTENDO,
    [Source.NINTENDO_DISCOUNTS]: Portal.NINTENDO,
    [Source.EPIC_SALES]: Portal.EPIC,
    [Source.STEAM_SALES]: Portal.STEAM,
    [Source.STEAM_BUNDLES]: Portal.STEAM,
    [Source.STEAM_IN_APP_SALES]: Portal.STEAM,
    [Source.STEAM_WISHLIST_BALANCE]: Portal.STEAM,
    [Source.STEAM_WISHLISTS]: Portal.STEAM,
    [Source.STEAM_IMPRESSIONS]: Portal.STEAM,
    [Source.STEAM_DISCOUNTS]: Portal.STEAM,
    [Source.META_QUEST_SALES]: Portal.META,
    [Source.META_RIFT_SALES]: Portal.META,
    [Source.PLAYSTATION_SALES]: Portal.PLAYSTATION,
    [Source.PLAYSTATION_WISHLIST_ACTIONS]: Portal.PLAYSTATION,
    [Source.APP_STORE_SALES]: Portal.APPLE,
    [Source.GOOGLE_SALES]: Portal.GOOGLE
} as const;

const manualLoginPortalToSourceMapping: Record<Portal, Source> = {
    [Portal.STEAM]: Source.STEAM_SALES,
    [Portal.META]: Source.META_RIFT_SALES,
    [Portal.MICROSOFT]: Source.MICROSOFT_SALES,
    [Portal.PLAYSTATION]: Source.PLAYSTATION_SALES,
    [Portal.NINTENDO]: Source.NINTENDO_SALES,
    [Portal.HUMBLE]: Source.HUMBLE_SALES,
    [Portal.GOG]: Source.GOG_SALES,
    [Portal.EPIC]: Source.EPIC_SALES,
    [Portal.APPLE]: Source.APP_STORE_SALES,
    [Portal.GOOGLE]: Source.GOOGLE_SALES
} as const;

/**
    Used in Electron renderer/.../scraperService
 */
export function sourceToPortal(source: Source): Portal {
    return sourceToPortalMapping[source];
}

export function manualLoginPortalToSource(portal: Portal): Source {
    return manualLoginPortalToSourceMapping[portal];
}
