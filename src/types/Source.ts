// please sync with https://gitlab.com/bluebrick/indiebi/data-pipeline/report-processor/-/blob/main/report_processor/dictionaries/sources.py

import {filterByPrefix} from '../utils/arrayUtils';

export enum Source {
    MICROSOFT_SALES = 'microsoft_sales',
    HUMBLE_SALES = 'humble_sales',
    GOG_SALES = 'gog_sales',
    NINTENDO_SALES = 'nintendo_sales',
    NINTENDO_WISHLISTS = 'nintendo_wishlists',
    NINTENDO_WII_DS_SALES = 'nintendo_wii_ds_sales',
    NINTENDO_PREORDERS = 'nintendo_preorders',
    NINTENDO_FREE_TO_PLAY = 'nintendo_free_to_play',
    NINTENDO_DISCOUNTS = 'nintendo_discounts',
    EPIC_SALES = 'epic_sales',
    STEAM_SALES = 'steam_sales',
    STEAM_WISHLISTS = 'steam_wishlists',
    STEAM_IMPRESSIONS = 'steam_impressions',
    STEAM_DISCOUNTS = 'steam_discounts',
    STEAM_IN_APP_SALES = 'steam_in_app_sales',
    STEAM_WISHLIST_BALANCE = 'steam_wishlist_balance',
    STEAM_BUNDLES = 'steam_bundles',
    META_RIFT_SALES = 'meta_rift_sales',
    META_QUEST_SALES = 'meta_quest_sales',
    PLAYSTATION_SALES = 'playstation_sales',
    PLAYSTATION_WISHLIST_ACTIONS = 'playstation_wishlist_actions',
    APP_STORE_SALES = 'app_store_sales',
    GOOGLE_SALES = 'google_sales'
}

// Sources which don't require feature flag
export const publicSources: Source[] = [
    Source.MICROSOFT_SALES,
    Source.HUMBLE_SALES,
    Source.GOG_SALES,
    Source.NINTENDO_SALES,
    Source.NINTENDO_DISCOUNTS,
    Source.NINTENDO_WISHLISTS,
    Source.EPIC_SALES,
    Source.STEAM_SALES,
    Source.STEAM_WISHLISTS,
    Source.STEAM_IMPRESSIONS,
    Source.META_QUEST_SALES,
    Source.META_RIFT_SALES,
    Source.PLAYSTATION_SALES,
    Source.APP_STORE_SALES,
    Source.GOOGLE_SALES
];

export const SourceValues = Object.values(Source);

export const filterSourcesByPrefix = (prefix) => filterByPrefix(SourceValues, prefix);

export const steamPoweredSources = [Source.STEAM_SALES, Source.STEAM_WISHLISTS, Source.STEAM_BUNDLES, Source.STEAM_IN_APP_SALES, Source.STEAM_WISHLIST_BALANCE];
export const steamGamesSources = [Source.STEAM_IMPRESSIONS, Source.STEAM_DISCOUNTS];
