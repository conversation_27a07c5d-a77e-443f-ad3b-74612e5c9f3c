/**
 * Any changes here should also be made in Scrapers-js and Scrapers-py!!
 */
import {deepStringify} from '../utils/deepStringify';

export const sensitiveFieldsArray = [
    'credentials',
    'apiToken',
    'password',
    'apiSetupData',
    'clientId',
    'tenantId',
    'totpSecret',
    'cloudStorageBucket',
    'token',
    'cookie',
    'cookies',
    'set-cookie',
    'clientSecret',
    'authorization',
    'spawnargs'
];

const REDACTED_MESSAGE = '[REDACTED]';

const filterParamsByName = (params: readonly string[], paramsToFilterOut: readonly string[]): string[] => {
    return params.filter((param) => !paramsToFilterOut.some((paramToIgnore) => param.startsWith(`--${paramToIgnore}`)));
};

export function censorSensitiveMessage(message: string): string {
    return sensitiveFieldsArray.reduce((acc, sensitiveField) => {
        //eslint-disable-next-line
        const utilsInspectFormatRegex = new RegExp(`["']?${sensitiveField}["']?:.*?,?`, 'ig');
        const isUtilsFormatCompliant = utilsInspectFormatRegex.test(message);
        isUtilsFormatCompliant ? (acc = REDACTED_MESSAGE) : acc;
        //eslint-disable-next-line
        const JsonStringifyRegex = new RegExp(`\\\"${sensitiveField}\\\":.*?,?`, 'ig');
        const isJsonFormatCompliant = JsonStringifyRegex.test(message);
        isJsonFormatCompliant ? (acc = REDACTED_MESSAGE) : acc;
        //eslint-disable-next-line
        const stringRegex = new RegExp(`${sensitiveField}\\s*:`, 'ig');
        const isStringFormatCompliant = stringRegex.test(message);
        isStringFormatCompliant ? (acc = REDACTED_MESSAGE) : acc;
        //eslint-disable-next-line
        const unixCmdParamFormatRegex = new RegExp(`--${sensitiveField}=[^\\s]*`, 'ig');
        const isBinaryParamsFormatCompliant = unixCmdParamFormatRegex.test(message);
        isBinaryParamsFormatCompliant ? (acc = REDACTED_MESSAGE) : acc;
        return acc;
    }, message);
}

export const filterSensitiveData = (params: readonly string[]): string[] => {
    return filterParamsByName(params, sensitiveFieldsArray);
};

export const filterSensitiveFields = (input?: Record<string, any>): Record<string, any> => {
    if (!input) {
        return {};
    }

    const inputWithoutCircularReference = JSON.parse(deepStringify(input));

    return Object.keys(inputWithoutCircularReference).reduce((acc, key) => {
        if (sensitiveFieldsArray.map((field) => field.toLowerCase()).includes(key.toLowerCase())) {
            // skip processing and do not add property to the output
            return acc;
        }

        if (typeof inputWithoutCircularReference[key] === 'object') {
            if (Array.isArray(inputWithoutCircularReference[key])) {
                return {
                    ...acc,
                    [key]: filterArray(inputWithoutCircularReference[key])
                };
            }

            return {
                ...acc,
                [key]: filterSensitiveFields(inputWithoutCircularReference[key])
            };
        }

        if (typeof inputWithoutCircularReference[key] === 'string') {
            return {
                ...acc,
                [key]: censorSensitiveMessage(inputWithoutCircularReference[key])
            };
        }

        return {
            ...acc,
            [key]: inputWithoutCircularReference[key]
        };
    }, {});
};

const filterArray = (input: any[]): any[] => {
    return input.map((element) => {
        if (typeof element === 'object') {
            return filterSensitiveFields(element);
        }

        if (typeof element === 'string') {
            return censorSensitiveMessage(element);
        }

        return element;
    });
};
