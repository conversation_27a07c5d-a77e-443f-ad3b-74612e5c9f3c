import {ReportInfo} from '../../api/types';
import {ScrapeResult} from '../../processes/types';
import {DateRange, Source} from '../../types';
import {NewLoginState, NewScraperState, TriggeredBy} from './scraperServiceEvents';

//TODO rename once log levels are cleared up
export enum ScraperServiceLogLevel {
    INFO = 1,
    WARN = 2,
    ERROR = 3
}

export interface TraceParams {
    logLevel?: ScraperServiceLogLevel;
    source?: Source;
    origin?: string;
    additionalData?: Record<string, any>;
    client_timestamp?: Date;
}

export interface iScraperServiceClient {
    close(): Promise<void>;

    updateToken(token: string): void;

    loadOutboxFromFile(): Promise<void>;

    startSendingOutbox(): void;

    scheduleScraperStateChangedEvent(params: {
        source: Source;
        newState: NewScraperState;
        operationId: string;
        triggeredBy: TriggeredBy;
        accountIdentifier?: string;
        reason?: string;
        dateRanges?: DateRange[];
    }): void;

    scheduleLoginStateChangedEvent(params: {
        source: Source;
        newState: NewLoginState;
        isManualSession: boolean;
        triggeredBy: TriggeredBy;
        operationId: string;
        accountIdentifier?: string;
        reason?: string;
        dateRanges?: DateRange[];
    }): void;

    scheduleReportUploadEvent(params: {scrapeResult: ScrapeResult; reportInfo: ReportInfo; operationId: string; source: Source}): void;

    scheduleTrace(message: string, operationId: string, params: TraceParams): void;
    loadIpInfo(): Promise<void>;
}
