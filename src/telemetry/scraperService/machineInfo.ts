import * as os from 'node:os';
import axios from 'axios';

export interface IpInfo {
    country: string;
    city: string;
    region: string;
}

export async function getIpInfo(): Promise<IpInfo> {
    try {
        const response = await axios.get('https://ipinfo.io/json');
        const {country, city, region} = response.data;
        return {
            country,
            city,
            region
        };
    } catch {
        return {
            country: 'Unknown',
            city: 'Unknown',
            region: 'Unknown'
        };
    }
}

export interface MachineInfo extends DirectHostInfo, Partial<IpInfo> {}

export interface DirectHostInfo {
    arch: string;
    homedir: string;
    hostname: string;
    platform: string;
    release: string;
    os_type: string;
    os_version: string;
}

export function getDirectHostInfo(): DirectHostInfo {
    return {
        arch: os.arch(),
        homedir: os.homedir(),
        hostname: os.hostname(),
        platform: os.platform(),
        release: os.release(),
        os_type: os.type(),
        os_version: os.version()
    };
}
