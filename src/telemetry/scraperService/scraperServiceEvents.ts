import {ReportInfo} from '../../api/types';
import {ScrapeResult} from '../../processes/types';
import {DateRange, Source} from '../../types';
import {MachineInfo} from './machineInfo';

export type NewScraperState = 'SCHEDULED' | 'STARTED' | 'STOPPED' | 'FINISHED' | 'FAILED' | 'DISABLED';
export type NewLoginState = 'SCHEDULED' | 'STARTED' | 'STOPPED' | 'CONFIGURED' | 'FAILED';

export enum TriggeredBy {
    SCHEDULE = 'SCHEDULE',
    SHADOW_TASK = 'SHADOW_TASK',
    USER_VIA_ELECTRON = 'USER_VIA_ELECTRON'
}

export abstract class S2BaseEvent {
    event_type: string;
    client_timestamp: Date;
    origin: string;
    body: object;
    operation_id: string;
}

export class ScraperStateChangedEvent extends S2BaseEvent {
    constructor({
        origin,
        newState,
        source,
        accountIdentifier,
        triggeredBy,
        operationId,
        reason,
        machineInfo,
        dateRanges
    }: {
        origin: string;
        newState: NewScraperState;
        source: Source;
        accountIdentifier: string;
        triggeredBy: TriggeredBy;
        operationId: string;
        reason?: string;
        machineInfo: MachineInfo;
        dateRanges?: DateRange[];
    }) {
        super();
        this.event_type = 'scraper_state_changed';
        this.client_timestamp = new Date();
        this.origin = origin;
        this.operation_id = operationId;
        this.body = {
            new_state: newState,
            triggered_by: triggeredBy,
            source,
            account_identifier: accountIdentifier,
            reason: reason,
            machine_info: machineInfo,
            date_ranges: dateRanges?.map((dateRange) => ({
                date_from: dateRange.dateFrom.toISOString().split('T')[0],
                date_to: dateRange.dateTo.toISOString().split('T')[0],
                days_in_range: dateRange.daysInRange
            }))
        };
    }
}

export class LoginStateChangedEvent extends S2BaseEvent {
    constructor({
        origin,
        newState,
        isManualSession,
        triggeredBy,
        source,
        accountIdentifier,
        operationId,
        reason,
        machineInfo
    }: {
        origin: string;
        newState: NewLoginState;
        isManualSession: boolean;
        triggeredBy: TriggeredBy;
        source: Source;
        accountIdentifier: string;
        operationId: string;
        reason?: string;
        machineInfo: MachineInfo;
    }) {
        super();
        this.event_type = 'login_state_changed';
        this.client_timestamp = new Date();
        this.origin = origin;
        this.operation_id = operationId;
        this.body = {
            new_state: newState,
            is_manual_session: isManualSession,
            triggered_by: triggeredBy,
            source,
            account_identifier: accountIdentifier,
            reason: reason,
            machine_info: machineInfo
        };
    }
}

export class ReportUploadEvent extends S2BaseEvent {
    constructor({
        origin,
        source,
        scrapeResult,
        reportInfo,
        operationId,
        machineInfo
    }: {
        origin: string;
        source: Source;
        scrapeResult: ScrapeResult;
        reportInfo: ReportInfo;
        operationId: string;
        machineInfo: MachineInfo;
    }) {
        super();
        this.event_type = 'report_uploaded';
        this.client_timestamp = new Date();
        this.origin = origin;
        this.operation_id = operationId;
        this.body = {
            source,
            scrape_result: {
                report_file_name: scrapeResult.reportFileName,
                start_date: scrapeResult.startDate,
                end_date: scrapeResult.endDate,
                source: scrapeResult.source,
                no_data: scrapeResult.noData
            },
            machine_info: machineInfo,
            report_info: reportInfo
        };
    }
}
