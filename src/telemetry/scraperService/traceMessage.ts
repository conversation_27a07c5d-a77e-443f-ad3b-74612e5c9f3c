import {ScraperServiceLogLevel} from '../../processes/types';
import {MachineInfo} from './machineInfo';

export interface TraceMessage {
    _id: string;
    message: string; // base64 encoded string
    level: ScraperServiceLogLevel;
    origin: string;
    custom_fields?: string; // base64 encoded Record<string, any>;
    client_timestamp: Date;
    source?: string;
    operation_id: string;
    machine_info: MachineInfo;
}
