import axios, {AxiosInstance} from 'axios';
import {v4 as uuidv4} from 'uuid';
import {version} from '../../../package.json';
import {ReportInfo} from '../../api/types';
import {ScraperServiceLogLevel} from '../../processes/types';
import {ScrapeResult} from '../../processes/types';
import {DateRange, Source} from '../../types';
import {deepStringify} from '../../utils/deepStringify';
import {retryAction} from '../../utils/retryAction';
import {filterSensitiveFields} from '../filterSensitiveData';
import {TraceParams, iScraperServiceClient} from './iScraperServiceClient';
import {MachineInfo, getDirectHostInfo, getIpInfo} from './machineInfo';
import {Outbox} from './outbox';
import {LoginStateChangedEvent, NewLoginState, NewScraperState, ReportUploadEvent, S2BaseEvent, ScraperStateChangedEvent, TriggeredBy} from './scraperServiceEvents';
import {TraceMessage} from './traceMessage';

export class HttpScraperServiceClient implements iScraperServiceClient {
    private axiosInstance: AxiosInstance;
    private timer: NodeJS.Timeout | undefined;
    private readonly intervalTime = 10 * 1000;
    private static readonly SEND_LOOP_MAX_ITERATIONS = 50;

    private machineInfo: MachineInfo = getDirectHostInfo();

    constructor(private readonly url: string, private token: string, private readonly origin: string, private outbox: Outbox) {
        this.axiosInstance = axios.create({
            headers: {
                Accept: 'application/json, text/plain, */*',
                'User-Agent': `ScraperLib/${version}`
            }
        });
    }

    public async close() {
        if (this.timer) {
            this.timer.unref();
            clearInterval(this.timer);
            this.timer = undefined;
        }
        // Run outbox dump to ensure all events are sent before closing
        await this.sendCurrentOutbox();

        // Save leftovers from outbox to file
        await this.outbox.dumpOutboxToFile();
    }

    public updateToken(token: string): void {
        this.token = token;
    }

    public async loadOutboxFromFile() {
        await this.outbox.loadOutboxFromFile();
    }

    public startSendingOutbox(): void {
        if (this.timer) {
            clearInterval(this.timer);
        }
        this.timer = setInterval(() => this.sendCurrentOutbox(), this.intervalTime);
    }

    public scheduleScraperStateChangedEvent({
        source,
        newState,
        triggeredBy,
        operationId,
        accountIdentifier,
        reason,
        dateRanges
    }: {
        source: Source;
        newState: NewScraperState;
        operationId: string;
        triggeredBy: TriggeredBy;
        accountIdentifier?: string;
        reason?: string;
        dateRanges?: DateRange[];
    }): void {
        const newEvent = new ScraperStateChangedEvent({
            origin: this.origin,
            newState,
            triggeredBy,
            source,
            accountIdentifier: accountIdentifier || 'Not provided',
            operationId: operationId,
            reason: reason,
            machineInfo: this.machineInfo,
            dateRanges
        });
        this.scheduleEvent(newEvent);
    }

    public scheduleLoginStateChangedEvent({
        source,
        newState,
        isManualSession,
        triggeredBy,
        operationId,
        accountIdentifier,
        reason
    }: {
        source: Source;
        newState: NewLoginState;
        isManualSession: boolean;
        triggeredBy: TriggeredBy;
        operationId: string;
        accountIdentifier?: string;
        reason?: string;
    }): void {
        const newEvent = new LoginStateChangedEvent({
            origin: this.origin,
            newState,
            isManualSession,
            triggeredBy,
            source,
            accountIdentifier: accountIdentifier || 'Not provided',
            operationId: operationId,
            reason: reason,
            machineInfo: this.machineInfo
        });
        this.scheduleEvent(newEvent);
    }

    public scheduleReportUploadEvent({
        scrapeResult,
        reportInfo,
        operationId,
        source
    }: {
        scrapeResult: ScrapeResult;
        reportInfo: ReportInfo;
        operationId: string;
        source: Source;
    }): void {
        const newEvent = new ReportUploadEvent({
            origin: this.origin,
            source,
            scrapeResult,
            reportInfo,
            operationId,
            machineInfo: this.machineInfo
        });
        this.scheduleEvent(newEvent);
    }

    private async sendScraperEvent(event: S2BaseEvent): Promise<void> {
        this.scheduleTrace(`Event sent: ${event.event_type} with params: ${JSON.stringify(filterSensitiveFields(event.body))}`, event.operation_id, {
            client_timestamp: event.client_timestamp
        });

        return await this.axiosInstance.post(`${this.url}/external/scraper_events`, event, {
            headers: {Authorization: `Bearer ${this.token}`}
        });
    }

    private scheduleEvent(event: S2BaseEvent): void {
        this.outbox.addEvents([event]);
    }

    public scheduleTrace(
        message: string,
        operationId: string,
        {logLevel = ScraperServiceLogLevel.INFO, source, origin = this.origin, additionalData = {}, client_timestamp = new Date()}: TraceParams = {}
    ): void {
        additionalData.manager_version = this.origin;

        this.outbox.addMessages([
            {
                _id: uuidv4(),
                message: Buffer.from(message).toString('base64'),
                level: logLevel,
                origin,
                custom_fields: Buffer.from(deepStringify(additionalData)).toString('base64'),
                source,
                client_timestamp,
                operation_id: operationId,
                machine_info: this.machineInfo
            }
        ]);
    }

    private async sendTraces(traceMessages: TraceMessage[]): Promise<void> {
        await this.axiosInstance
            .put(`${this.url}/external/traces`, traceMessages, {
                headers: {Authorization: `Bearer ${this.token}`}
            })
            .catch((error) => {
                if (error.response) {
                    // Server responded with a non-2xx status code
                    console.log(`Request failed with status: ${error.response.status}`);
                    console.log('Response body:', JSON.stringify(error.response.data));
                } else if (error.request) {
                    // Request was made but no response was received
                    console.log('No response received:', error.request);
                } else {
                    // Error setting up the request
                    console.log('Error:', error.message);
                }

                // Re-throw the error after logging
                throw error;
            });
    }

    private async processEventSending(event: ScraperStateChangedEvent): Promise<void> {
        let eventSafetyCounter = 0;
        let eventToSend: ScraperStateChangedEvent | undefined = event;
        while (eventToSend && eventSafetyCounter < HttpScraperServiceClient.SEND_LOOP_MAX_ITERATIONS) {
            try {
                await retryAction({
                    target: async () => this.sendScraperEvent(eventToSend!),
                    delay: 500,
                    backoff: 2,
                    maxAttempts: 4,
                    logger: {
                        // this is to prevent endless loop of sending traces (sending trace triggers sending another trace from retryAction)
                        info: console.log,
                        error: console.error
                    },
                    label: `Sending event ${eventToSend.event_type} to scraper service`
                });
            } catch {
                //TODO not safe since a partial fail can interrupt the flow of events
                this.outbox.addEvents([eventToSend]);
            }
            eventToSend = this.outbox.getEventToSend();
            eventSafetyCounter++;
        }
    }

    private async processTraceSending(traceBatch: TraceMessage[]) {
        let safetyCounter = 0;
        let batchToSend = traceBatch;

        while (batchToSend.length > 0 && safetyCounter < HttpScraperServiceClient.SEND_LOOP_MAX_ITERATIONS) {
            try {
                await retryAction({
                    target: async () => this.sendTraces(batchToSend),
                    delay: 500,
                    backoff: 2,
                    maxAttempts: 4,
                    logger: {
                        // this is to prevent endless loop of sending traces (sending trace triggers sending another trace from retryAction)
                        info: console.log,
                        error: console.error
                    },
                    label: `Sending ${batchToSend.length} traces to scraper service`
                });
            } catch {
                // TODO not async safe, may overwrite some messages if it takes more than interval time
                this.outbox.addMessages(batchToSend);
            }
            batchToSend = this.outbox.getTraceBatchToSend();
            safetyCounter++;
        }
    }

    private async sendCurrentOutbox() {
        const batchToSend = this.outbox.getTraceBatchToSend();
        const eventToSend = this.outbox.getEventToSend();

        if (eventToSend) {
            await this.processEventSending(eventToSend);
        }

        if (batchToSend.length !== 0) {
            await this.processTraceSending(batchToSend);
        }
    }

    public async loadIpInfo() {
        const ipInfo = await getIpInfo();
        this.machineInfo = {
            ...this.machineInfo,
            ...ipInfo
        };
    }
}
