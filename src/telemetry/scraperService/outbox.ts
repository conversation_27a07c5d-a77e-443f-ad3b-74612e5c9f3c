import {promises as fs} from 'fs';
import {checkFileExists, createOrOverrideFile} from '../../utils/fileUtils';
import {ScraperStateChangedEvent} from './scraperServiceEvents';
import {TraceMessage} from './traceMessage';

export class Outbox {
    private traceMessages: TraceMessage[] = [];
    private events: ScraperStateChangedEvent[] = [];
    private readonly defaultBatchSize: number = 50;

    constructor(private readonly hddDumpFilePath: string) {}

    /**
     * This method pulls out messages and modifies the outbox content
     * @param maxRequestedBatchSize
     */
    public getTraceBatchToSend(maxRequestedBatchSize?: number): TraceMessage[] {
        //TODO mark messages as sent instead of modifying the outbox
        const actualBatchSize = Math.min(this.traceMessages.length, maxRequestedBatchSize || this.defaultBatchSize);
        return this.traceMessages.splice(0, actualBatchSize);
    }

    public getEventToSend(): <PERSON>raperStateChangedEvent | undefined {
        return this.events.shift();
    }

    public addMessages(messages: TraceMessage[]) {
        this.traceMessages = this.traceMessages.concat(messages);
    }

    public addEvents(scraperStateChangedEvents: ScraperStateChangedEvent[]) {
        this.events = this.events.concat(scraperStateChangedEvents);
    }

    /**
     * This method dumps the outbox to a file and clears the outbox
     */
    public async dumpOutboxToFile() {
        if (!this.traceMessages.length && !this.events.length) {
            return;
        }

        await createOrOverrideFile(this.hddDumpFilePath);
        const messagesAsTraces = this.traceMessages.map((message: TraceMessage) => JSON.stringify(message));
        const eventsAsTraces = this.events.map((event: ScraperStateChangedEvent) => JSON.stringify(event));
        await fs.writeFile(this.hddDumpFilePath, [...eventsAsTraces, ...messagesAsTraces].join('\n'));

        this.traceMessages = [];
        this.events = [];
    }

    public async loadOutboxFromFile(): Promise<void> {
        if (!(await checkFileExists(this.hddDumpFilePath))) {
            return;
        }

        try {
            const fileContent = await fs.readFile(this.hddDumpFilePath, 'utf8');
            const jsonItems = fileContent.split('\n').filter((line) => line.trim());

            const traceMessages: TraceMessage[] = [];
            const events: ScraperStateChangedEvent[] = [];

            for (const jsonItem of jsonItems) {
                try {
                    const item = JSON.parse(jsonItem);
                    if (item.event_type) {
                        events.push({...item, client_timestamp: new Date(item.client_timestamp)});
                    } else {
                        traceMessages.push({...item, client_timestamp: new Date(item.client_timestamp)});
                    }
                } catch (parseError) {
                    console.error('Error parsing JSON item', parseError);
                }
            }

            if (traceMessages.length > 0) {
                this.addMessages(traceMessages);
            }

            if (events.length > 0) {
                this.addEvents(events);
            }
        } catch (e) {
            console.error('Error reading outbox file', e);
        }
    }
}
