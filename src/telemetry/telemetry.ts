import {errorType} from '../configurations/errorType';
import {ScraperServiceLogLevel} from '../processes/types';
import {ScraperLibError} from '../processes/types/errors';
import {Source} from '../types';
import {censorSensitiveMessage, filterSensitiveFields} from './filterSensitiveData';
import {iScraperServiceClient} from './scraperService/iScraperServiceClient';

export const GENERIC_OPERATION_ID = 'generic';

let s2Client: iScraperServiceClient;

export const dummyS2Client: iScraperServiceClient = {
    /* eslint-disable @typescript-eslint/no-empty-function */
    scheduleTrace: () => {},
    close: () => Promise.resolve(),
    loadOutboxFromFile: () => Promise.resolve(),
    scheduleScraperStateChangedEvent: () => undefined,
    scheduleLoginStateChangedEvent: () => undefined,
    /* eslint-disable @typescript-eslint/no-empty-function */
    startSendingOutbox: () => {},
    /* eslint-disable @typescript-eslint/no-empty-function */
    updateToken(_token: string) {},
    scheduleReportUploadEvent: () => undefined,
    loadIpInfo: () => Promise.resolve()
};

export const setS2Client = (client: iScraperServiceClient): void => {
    s2Client = client;
};

function ensureS2Client(): void {
    if (process.env.NODE_ENV === 'test') {
        if (!s2Client) {
            s2Client = dummyS2Client;
        }
        return;
    }
    if (!s2Client) {
        throw new ScraperLibError(errorType.INTERNAL_SCRAPER_LIB_ERROR, 'error', {
            message: 'Telemetry client is not available. Make sure to call setS2Client before using telemetry functions.'
        });
    }
}

export function trace(
    message: string,
    parameters: {
        logLevel?: ScraperServiceLogLevel;
        operationId?: string;
        source?: Source;
        additionalData?: Record<string, any>;
        origin?: string;
    } = {}
) {
    ensureS2Client();
    const censoredMessage = censorSensitiveMessage(message);
    const {operationId = GENERIC_OPERATION_ID, logLevel = ScraperServiceLogLevel.INFO, source, origin, additionalData} = parameters;

    s2Client.scheduleTrace(censoredMessage, operationId, {logLevel, source, origin, additionalData});
}

export function exception(
    error: Error,
    crash = false,
    customTelemetryDimensions: Record<string, any> = {},
    operationId: string = GENERIC_OPERATION_ID,
    source?: Source
): void {
    ensureS2Client();
    const safeCustomTelemetryDimensions = filterSensitiveFields(customTelemetryDimensions);
    const safeError = filterSensitiveFields(error);
    s2Client.scheduleTrace(error.message || 'Unknown error', operationId, {
        logLevel: ScraperServiceLogLevel.ERROR,
        source,
        additionalData: {
            ...safeCustomTelemetryDimensions,
            ...safeError,
            crash
        }
    });
}

export function logAndThrowError(error: string | Error, customTelemetryDimensions: Record<string, any> = {}, crash = false): never {
    const e = typeof error === 'string' ? new Error(error) : error;
    try {
        exception(e, crash, customTelemetryDimensions);
    } catch (telemetryError) {
        // If telemetry fails, we still want to throw the original error
        console.error('Failed to log error to telemetry:', telemetryError);
    }
    throw e;
}
