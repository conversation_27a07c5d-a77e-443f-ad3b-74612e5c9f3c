import * as Sentry from '@sentry/node';

let sentryClient: Sentry.NodeClient | undefined = undefined;

export function initSentry(dsn: string) {
    if (dsn) {
        sentryClient = Sentry.init({
            dsn: dsn,
            enabled: true,
            includeLocalVariables: true,
            tracesSampleRate: 0.0
        });
    }
}

export async function closeSentry() {
    if (sentryClient) {
        await sentryClient.close();
    }
}

export function withSentryErrorHandling() {
    return function (_target: any, _propertyKey: string, descriptor: TypedPropertyDescriptor<(...args: any[]) => Promise<any>>) {
        const originalMethod = descriptor.value!;

        descriptor.value = async function (...args: any[]) {
            try {
                return await originalMethod.apply(this, args);
            } catch (error) {
                if (sentryClient) {
                    Sentry.captureException(error);
                }
                throw error;
            }
        };

        return descriptor;
    };
}
