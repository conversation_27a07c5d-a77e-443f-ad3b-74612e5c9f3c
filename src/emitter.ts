import {Source} from './types';
import {EventType} from './types/EventsTypes';
import {deepStringify} from './utils/deepStringify';

export type ScraperLibEmitterObject = {code?: string; message: string};
export type ScraperLibEmitter = (emitObject: ScraperLibEmitterObject) => void;

export type EmitterProps = {
    type: EventType;
    source?: Source;
    message?: string;

    // fallback for new props
    [key: string]: any;
};

export class Emitter {
    private static instance: Emitter;

    public static getInstance(): Emitter {
        if (!Emitter.instance) {
            Emitter.instance = new Emitter();
        }

        return Emitter.instance;
    }
    private emitter: ScraperLibEmitter = () => {
        return;
    };

    public static setEmitter(emitter: ScraperLibEmitter): void {
        Emitter.getInstance().emitter = emitter;
    }

    public static emit(props: EmitterProps): void {
        const event = deepStringify({...props});
        Emitter.getInstance().emitter({message: event});
    }
}
