import {ErrorType} from '../configurations/errorType';
import {ScraperConfigurationStatus} from '../configurations/ScraperConfiguration';
import {SourceAccount} from '../configurations/SourceAccount';
import {Source} from '../types';

export interface PublicScraperConfiguration {
    id: string;
    source: Source;
    activeRun?: {
        percentageProgress: number;
        lastSuccessfulScrapeDate?: Date;
        numOfDataRangesToScrape?: number;
        scrapedDays?: number;
        numberOfDaysToScrape?: number;
        startDate: Date;
    };
    lastSuccessfulScrapeDate?: Date;
    status: ScraperConfigurationStatus;
    errorType?: ErrorType;
    previousStatus?: ScraperConfigurationStatus;
    previousErrorType?: ErrorType;
    sourceAccountId: SourceAccount['id'];
    createdAt?: Date;
}
