import {ErrorType, errorType} from '../configurations/errorType';
import {EphemeralScraperConfigStates, ScraperConfiguration, ScraperConfigurationEditParams, ScraperConfigurationStatus} from '../configurations/ScraperConfiguration';
import {Emitter} from '../emitter';
import {Storage} from '../storage/Storage';
import * as telemetry from '../telemetry/telemetry';
import {ShadowModeTask, Source, eventTypes} from '../types';

type EmitScraperRunUpdateParams = {
    progress?: number;
    numberOfDaysToScrape?: number;
    scrapedDays?: number;
    errorType?: ErrorType;
};

export type UpdateStatus = (newStatus: ScraperConfigurationStatus, additionalParams?: EmitScraperRunUpdateParams) => Promise<void>;
export type UpdateLastScrapeDate = (source: Source) => Promise<void>;

export function getStatusHelpers(storage: Storage, scraperConfig: ScraperConfiguration): {updateStatus: UpdateStatus; updateLastScrapeDate: UpdateLastScrapeDate} {
    return {
        async updateStatus(newStatus: ScraperConfigurationStatus, additionalParams?: EmitScraperRunUpdateParams): Promise<void> {
            telemetry.trace(`Updating state for run ${scraperConfig.source} new state is: ${newStatus}`);

            if (isGoogleFailedLikelyDueToPendingPermissions(scraperConfig, newStatus, additionalParams?.errorType)) {
                telemetry.trace(`Converting INCORRECT_CREDENTIALS to PENDING_PERMISSIONS for ${scraperConfig.source}, because 48 hours not passed yet since creation`);
                newStatus = ScraperConfigurationStatus.PENDING_PERMISSIONS;
                additionalParams!.errorType = undefined;
            }

            const updateParams: ScraperConfigurationEditParams = {
                source: scraperConfig.source,
                status: newStatus,
                errorType: additionalParams?.errorType,
                previousStatus: scraperConfig.status,
                previousErrorType: scraperConfig.errorType
            };
            telemetry.trace('Source state update', {additionalData: updateParams});
            const isStatusThatShouldBeUpdated = !EphemeralScraperConfigStates.includes(newStatus);
            if (isStatusThatShouldBeUpdated) {
                try {
                    await storage.editScraperConfiguration(updateParams);
                } catch (error) {
                    telemetry.exception(error, false, {operation: 'updateStatus', scraperConfig: scraperConfig});
                    console.error('Failed to update scraper configuration status', error);
                    return;
                }
            }
            Emitter.emit({type: eventTypes.scraperRunUpdate, source: scraperConfig.source, newStatus, ...additionalParams});
        },
        async updateLastScrapeDate(source: Source): Promise<void> {
            await storage.editScraperConfiguration({source, lastSuccessfulScrapeDate: new Date()});
        }
    };
}

export function isGoogleFailedLikelyDueToPendingPermissions(scraperConfig: ScraperConfiguration, newStatus: ScraperConfigurationStatus, errType?: ErrorType): boolean {
    const getHoursSince = (date: Date): number => (new Date().getTime() - date.getTime()) / 1000 / 60 / 60;
    return (
        scraperConfig.source === Source.GOOGLE_SALES &&
        newStatus === ScraperConfigurationStatus.ERROR &&
        errType === errorType.INCORRECT_CREDENTIALS &&
        !!scraperConfig.createdAt &&
        getHoursSince(scraperConfig.createdAt) < 48
    );
}

export function getShadowModeStatusHelpers({id, label}: ShadowModeTask): {updateStatus: UpdateStatus; updateLastScrapeDate: UpdateLastScrapeDate} {
    return {
        async updateStatus(newStatus: ScraperConfigurationStatus, additionalParams?: EmitScraperRunUpdateParams): Promise<void> {
            telemetry.trace(`ShadowMode[${id}][${label}]: newStatus: ${newStatus}` + (additionalParams ? `, additionalParams: ${JSON.stringify(additionalParams)}` : ''));
        },
        async updateLastScrapeDate(source: Source) {
            telemetry.trace(`ShadowMode[${id}][${label}]: source: ${source}, updateLastScrapeDate: ${new Date()}`);
        }
    };
}
