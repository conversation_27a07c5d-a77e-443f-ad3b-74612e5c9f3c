import {ScraperConfiguration, ScraperConfigurationStatus} from '../configurations/ScraperConfiguration';
import {ScraperConfigurationManager} from '../configurations/ScraperConfigurationManager';
import {SourceAccount} from '../configurations/SourceAccount';
import {BinaryProxy, CheckSessionParams, CommonParams, LoginParams, ScrapeParams} from '../dependencies/BinaryProxy';
import {DependenciesManager} from '../dependencies/DependenciesManager';
import {getFeatureFlags} from '../featureFlags/featureFlags';
import {BinaryLoginResult, CheckSessionResult, ManualLoginDetailsResult, SourceSideOrganization} from '../processes/types';
import {Storage} from '../storage/Storage';
import {iScraperServiceClient} from '../telemetry/scraperService/iScraperServiceClient';
import {TriggeredBy} from '../telemetry/scraperService/scraperServiceEvents';
import {Command, DateRange, ShadowModeTask, Source} from '../types';
import {sumDaysInDateRanges} from '../utils/datesUtils';
import {buildSessionPath, copySessionFileForShadowRun} from '../utils/session';
import {Credentials, sourceAccountToCredentials, sourceAccountToScrapeCredentials} from './credentials';
import {BaseContext, LoginWithCookiesRunContext, ScrapeRunContext} from './jobs/context';
import {EnsureValidSessionJob} from './jobs/EnsureValidSessionJob';
import {GetManualLoginDataJob} from './jobs/GetManualLoginDataJob';
import {GetSourceSideOrganizationsJob} from './jobs/GetSourceSideOrganizationsJob';
import {LoginWithCookiesRun} from './LoginWithCookiesRun';
import {LoginWithCredentialsRun} from './LoginWithCredentialsRun';
import {getSourcePriority} from './PriorityQueue';
import {Run} from './Run';
import {ScrapeRun} from './ScrapeRun';
import {UpdateStatus, getShadowModeStatusHelpers, getStatusHelpers} from './status';

export class RunFactory {
    constructor(
        private readonly mainDir: string,
        private readonly storage: Storage,
        private readonly dependenciesManager: DependenciesManager,
        private readonly binaryProxy: BinaryProxy,
        private readonly s2Client: iScraperServiceClient,
        private readonly scraperConfigurationManager: ScraperConfigurationManager
    ) {}

    async createScrapeRun(
        source: Source,
        dateRanges: DateRange[],
        scraperConfiguration: ScraperConfiguration,
        triggeredBy: TriggeredBy,
        shadowModeTask?: ShadowModeTask
    ): Promise<Run<ScrapeRunContext>> {
        const sourceAccount = await this.getSourceAccount(scraperConfiguration);
        const commonParams: CommonParams = await this.getCommonParams(source, sourceAccount.sessionPath, shadowModeTask);

        const scrapeParams: Omit<ScrapeParams, 'dateFrom' | 'dateTo'> = {
            ...commonParams,
            skuToIgnore: scraperConfiguration.skuToIgnore,
            credentials: sourceAccountToScrapeCredentials(source, sourceAccount)
        };

        const loginParams: LoginParams = {
            ...commonParams,
            credentials: sourceAccountToCredentials(source, sourceAccount)
        };

        const checkSessionParams: CheckSessionParams = {...commonParams};

        const {updateStatus, updateLastScrapeDate} = shadowModeTask ? getShadowModeStatusHelpers(shadowModeTask) : getStatusHelpers(this.storage, scraperConfiguration);

        return new ScrapeRun(
            {
                mainDir: this.mainDir,
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                s2Client: this.s2Client,
                checkSessionParams,
                loginParams,
                scrapeParams,
                updateStatus,
                updateLastScrapeDate,
                dateRanges
            },
            {
                priority: getSourcePriority(source),
                context: {
                    source,
                    command: Command.SCRAPE,
                    shadowModeTask,
                    numberOfDaysToScrape: sumDaysInDateRanges(dateRanges),
                    dateRangesToProcess: dateRanges,
                    forceRunStart: false,
                    triggeredBy,
                    mutable: {},
                    sessionPath: 'fake'
                }
            }
        );
    }

    async createGetSourceSideOrganizationsRun(
        sourceConfiguration: ScraperConfiguration,
        triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON
    ): Promise<Run<BaseContext, SourceSideOrganization[]>> {
        const sourceAccount = await this.getSourceAccount(sourceConfiguration);
        const commonParams: CommonParams = await this.getCommonParams(sourceConfiguration.source, sourceAccount.sessionPath);
        return new Run<BaseContext, SourceSideOrganization[]>([new GetSourceSideOrganizationsJob(this.binaryProxy, commonParams)], {
            context: {source: sourceConfiguration.source, forceRunStart: false, command: Command.GET_SOURCE_SIDE_ORGANIZATIONS, triggeredBy}
        });
    }

    async createGetManualLoginDataRun(source: Source, triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON): Promise<Run<BaseContext, ManualLoginDetailsResult>> {
        const commonParams: CommonParams = await this.getCommonParams(source, undefined);
        return new Run<BaseContext, ManualLoginDetailsResult>([new GetManualLoginDataJob(this.binaryProxy, commonParams)], {
            context: {source, forceRunStart: true, command: Command.GET_MANUAL_LOGIN_DETAILS, triggeredBy}
        });
    }
    //TODO: make shadowModeTask required as we always pass it
    async createShadowModeLoginWithCredentialsRun(
        source: Source,
        scraperConfiguration: ScraperConfiguration,
        shadowModeTask?: ShadowModeTask
    ): Promise<Run<BaseContext, BinaryLoginResult>> {
        const sourceAccount = await this.getSourceAccount(scraperConfiguration);

        const loginParams: LoginParams = {
            ...(await this.getCommonParams(source, sourceAccount.sessionPath, shadowModeTask)),
            credentials: sourceAccountToCredentials(source, sourceAccount)
        };

        return new LoginWithCredentialsRun(
            {
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                s2Client: this.s2Client,
                loginParams
            },
            {
                context: {
                    source,
                    command: Command.LOGIN,
                    forceRunStart: true,
                    triggeredBy: TriggeredBy.SHADOW_TASK,
                    shadowModeTask,
                    sessionPath: sourceAccount.sessionPath,
                    mutable: {}
                }
            }
        );
    }

    async createLoginWithCredentialsRun(
        source: Source,
        credentials: Credentials,
        sessionPath: string,
        triggeredBy: TriggeredBy
    ): Promise<Run<BaseContext, BinaryLoginResult>> {
        const commonParams: CommonParams = await this.getCommonParams(source, sessionPath);
        const loginParams: LoginParams = {...commonParams, credentials};

        return new LoginWithCredentialsRun(
            {
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                s2Client: this.s2Client,
                loginParams
            },
            {
                context: {
                    source,
                    command: Command.LOGIN,
                    forceRunStart: true,
                    triggeredBy,
                    sessionPath,
                    mutable: {}
                }
            }
        );
    }

    async createLoginWithCookiesRun(source: Source, triggeredBy: TriggeredBy, cookies: any[]): Promise<Run<LoginWithCookiesRunContext, ScraperConfiguration[]>> {
        return new LoginWithCookiesRun(
            {
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                s2Client: this.s2Client,
                scraperConfigurationManager: this.scraperConfigurationManager
            },
            {
                context: {
                    source,
                    forceRunStart: true,
                    triggeredBy,
                    sessionPath: buildSessionPath(this.mainDir, source),
                    mutable: {},
                    command: Command.LOGIN, //TODO what is command for, is this not binary related?
                    cookies
                }
            }
        );
    }

    async createCheckSessionRun(source: Source, sessionPath: string, triggeredBy: TriggeredBy): Promise<Run<BaseContext, CheckSessionResult>> {
        const commonParams: CommonParams = await this.getCommonParams(source, sessionPath);
        const loginParams: LoginParams = {...commonParams, credentials: undefined};
        const checkSessionParams: CheckSessionParams = {...commonParams};

        // eslint-disable-next-line @typescript-eslint/no-empty-function
        const emptyUpdateStatus: UpdateStatus = async (_newStatus: ScraperConfigurationStatus, _additionalParams?: any): Promise<void> => {};

        return new Run<BaseContext, CheckSessionResult>([new EnsureValidSessionJob(this.binaryProxy, checkSessionParams, loginParams, emptyUpdateStatus)], {
            context: {source, forceRunStart: true, command: Command.CHECK_SESSION, triggeredBy}
        });
    }

    async getCommonParams(source: Source, sessionPath: string | undefined, shadowModeTask?: ShadowModeTask): Promise<CommonParams> {
        const sessionPath_ = shadowModeTask && sessionPath ? await copySessionFileForShadowRun(sessionPath) : sessionPath;

        const forcedExecPaths = shadowModeTask ? this.dependenciesManager.getExecPathsForShadowRun(shadowModeTask) : {};

        return {
            ...forcedExecPaths,
            source,
            sessionPath: sessionPath_,
            screenshotAndHtmlDumpPath: await this.storage.getScreenshotAndHtmlDumpDirPath(),
            featureFlags: getFeatureFlags()
        };
    }

    async getSourceAccount(scraperConfig: ScraperConfiguration): Promise<SourceAccount> {
        const sourceAccount = await this.storage.getSourceAccount(scraperConfig.sourceAccountId);
        if (!sourceAccount) {
            throw new Error('Linked source account not found.');
        }
        return sourceAccount;
    }
}
