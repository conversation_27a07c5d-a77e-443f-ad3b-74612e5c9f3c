import * as telemetry from '../telemetry/telemetry';
import {Command, Source} from '../types';
import {getRelatedSources} from '../utils/sourceUtils';
import {Run} from './Run';

/**
 * This class is a mix of priority queue and worker pool.
 * It's main job is to keep track of runs and execute them in a priority order.
 */
export default class PriorityQueue {
    private currentlyRunning: Run[] = [];
    private waitingQueue: Run[] = [];
    public readonly queUpdateInterval: number;
    /**
     * The concurrent run limit is a `soft` limit. It can be exceeded since we do not want to force the user
     * to wait for a run to finish before starting a new one.
     * Currently true for login related runs like getManualLoginDetails and login.
     */
    public readonly softConcurrentRunLimit: number;
    private timer: NodeJS.Timeout | undefined;

    constructor(softConcurrentRunLimit: number, queUpdateInterval = 3000) {
        this.softConcurrentRunLimit = softConcurrentRunLimit;
        this.queUpdateInterval = queUpdateInterval;
    }

    public startInterval() {
        telemetry.trace('Starting priority que', {additionalData: {interval: this.queUpdateInterval}});
        this.timer = setInterval(this.fillUpRunningQueue.bind(this), this.queUpdateInterval);
    }

    private addToRunningQueue(run: Run) {
        this.currentlyRunning.push(run);
        run.executeAllJobs().catch((e) => {
            telemetry.trace('Run failed');
            telemetry.exception(e, true, {operation: 'run'});
        });
    }

    /**
     * This function is NOT async by design. It's only function is and should be to fill up the queue with runs.
     * It should NOT wait for any of the runs to finish since that is the job of the runs themselves.
     *
     * Since this function is the main function of the queue it is also responsible for cleaning the queue and the currently running array from non-active runs.
     * Currently, it is made public for testing purposes (technically there is nothing wrong with it being public, but it's kind of a smell to me).//TODO
     */
    public fillUpRunningQueue() {
        this.currentlyRunning = this.filterActiveRunsInArray(this.currentlyRunning);
        this.waitingQueue = this.filterActiveRunsInArray(this.waitingQueue);

        // Create a new array to store runs that couldn't be started because of related sources
        const skippedRuns: Run[] = [];

        while (this.waitingQueue.length > 0 && this.currentlyRunning.length < this.softConcurrentRunLimit) {
            const nextItem = this.waitingQueue.shift();
            if (nextItem) {
                if (this.isRunWithRelatedSourceAlreadyRunning(nextItem)) {
                    skippedRuns.push(nextItem);
                } else {
                    this.addToRunningQueue(nextItem);
                }
            }
        }

        // Add back all skipped runs to the queue
        this.waitingQueue.unshift(...skippedRuns);
        this.waitingQueue.sort((a, b) => b.priority - a.priority);
    }

    async killAll() {
        const runsToKill = [...this.waitingQueue, ...this.currentlyRunning];

        this.waitingQueue = [];
        this.currentlyRunning = [];

        await Promise.all(
            runsToKill.map((run) => {
                run.kill().catch(telemetry.exception);
            })
        );
    }

    async killBySourceAndCommand(source: Source, command: Command, isShadowRun = false) {
        const allRuns = [...this.waitingQueue, ...this.currentlyRunning];
        const runsToBeKilled = allRuns.filter(({context}) => context.source === source && context.isShadowRun === isShadowRun && context.command == command);

        await Promise.all(runsToBeKilled.map((run) => run.kill()));
        this.fillUpRunningQueue();
    }

    /**
     * Adds a new run to the queue, sorts the que by priority (higher is better) and starts runs up to it's concurrentRunLimit.
     * If a run is added with a higher priority than the currently running runs, it will NOT be started immediately but rather put on the top of the waiting que.
     * Some runs have a forceRunStart flag which will make them start immediately regardless of the priority. This is done to always allow login operations to start immediately
     * since they are blocking the user from using the app.
     *
     * @param run
     *
     * --- Design notes ---
     *
     * This function is made NON async by design!
     * It's main job is to add a run to the queue and sort it by priority.
     * Starting runs by running the fillUpRunningQueue is an optimization so that users do not have to wait the cycle sleep time for all runs to start
     * (it also helps A LOT in testing).
     *
     */
    public add(run: Run): void {
        if (run.context.forceRunStart) {
            this.addToRunningQueue(run);
            return;
        }
        this.waitingQueue.push(run);
        this.waitingQueue.sort((a, b) => b.priority - a.priority);
        this.fillUpRunningQueue();
    }

    public getCurrentlyRunning(): Run[] {
        return this.currentlyRunning;
    }

    public getQueue(): Run[] {
        return this.waitingQueue;
    }

    public stopInterval() {
        if (this.timer) {
            telemetry.trace('Stopping priority que');
            this.timer.unref();
            clearInterval(this.timer);
        }
    }

    private isRunWithRelatedSourceAlreadyRunning(run: Run): boolean {
        // Check if any currently running source is in the related sources list
        // but not the source itself (a source is always related to itself)
        if (this.currentlyRunning.length === 0) {
            return false;
        }

        const relatedSources = getRelatedSources(run.context.source);

        return this.currentlyRunning.some((runningRun) => {
            if (runningRun.context.source === run.context.source) {
                return false;
            }

            return relatedSources.includes(runningRun.context.source);
        });
    }

    private filterActiveRunsInArray(runs: Run[]) {
        return runs.filter((run) => run.isActive());
    }
}

const SOURCE_PRIORITY_MAP: Partial<Record<Source, number>> = {
    [Source.STEAM_WISHLIST_BALANCE]: -1
};

export const getSourcePriority = (source: Source): number => {
    return SOURCE_PRIORITY_MAP[source] ?? 0;
};
