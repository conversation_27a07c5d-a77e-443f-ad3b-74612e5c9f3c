import {errorType} from '../configurations/errorType';
import {ScraperConfiguration, ScraperConfigurationStatus} from '../configurations/ScraperConfiguration';
import {ScraperConfigurationManager} from '../configurations/ScraperConfigurationManager';
import {BinaryProxy, CommonParams} from '../dependencies/BinaryProxy';
import {DependenciesManager} from '../dependencies/DependenciesManager';
import {iScraperServiceClient} from '../telemetry/scraperService/iScraperServiceClient';
import * as telemetry from '../telemetry/telemetry';
import {removeFileOrDirectory} from '../utils/fileUtils';
import {LoginWithCookiesRunContext} from './jobs/context';
import {EnsureValidSessionJob} from './jobs/EnsureValidSessionJob';
import {SaveConfigurationJob} from './jobs/SaveConfigurationJob';
import {SaveCookiesToSessionJob} from './jobs/SaveCookiesToSessionJob';
import {SyncDependenciesJob} from './jobs/SyncDependenciesJob';
import {Run} from './Run';
import {UpdateStatus} from './status';

export class LoginWithCookiesRun extends Run<LoginWithCookiesRunContext, ScraperConfiguration[]> {
    constructor(
        dependencies: {
            dependenciesManager: DependenciesManager;
            binaryProxy: BinaryProxy;
            s2Client: iScraperServiceClient;
            scraperConfigurationManager: ScraperConfigurationManager;
        },
        options: {
            context?: Omit<LoginWithCookiesRunContext, 'operationId' | 'isShadowRun'>;
        } = {}
    ) {
        /* eslint-disable @typescript-eslint/no-empty-function */
        const updateStatus: UpdateStatus = async (_newStatus: ScraperConfigurationStatus, _additionalParams?: any): Promise<void> => {};
        const commonParams: CommonParams = {
            sessionPath: options.context?.sessionPath,
            source: options.context!.source
        };

        super(
            [
                new SyncDependenciesJob(dependencies.dependenciesManager),
                new SaveCookiesToSessionJob(),
                new EnsureValidSessionJob(
                    dependencies.binaryProxy,
                    commonParams,
                    {
                        ...commonParams,
                        credentials: {}
                    },
                    updateStatus
                ),
                new SaveConfigurationJob(dependencies.scraperConfigurationManager)
            ],
            {
                context: options.context
            }
        );

        this.setEventHandlers({
            onScheduled: async () => {
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'SCHEDULED',
                    isManualSession: true,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined
                });
            },
            onStart: async () => {
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'STARTED',
                    isManualSession: true,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined
                });
            },
            onSuccess: async () => {
                telemetry.trace(`Login successful for ${this.context.source}`);
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'CONFIGURED',
                    isManualSession: true,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined
                });
            },
            onFail: async (e: any) => {
                telemetry.trace(`Login failed for ${this.context.source}`);
                telemetry.exception(e);
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'FAILED',
                    isManualSession: true,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined,
                    reason: e?.errorType ?? errorType.UNEXPECTED_ERROR
                });
                await removeFileOrDirectory(this.context.sessionPath, false);
            },
            onKill: async () => {
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'STOPPED',
                    isManualSession: true,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined
                });
                await removeFileOrDirectory(this.context.sessionPath, false);
            }
        });
    }
}
