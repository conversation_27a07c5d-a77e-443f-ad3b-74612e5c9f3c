import {SourceAccount} from '../configurations/SourceAccount';
import {MISSING_SCRAPE_CONFIGURATION_ERROR_MSG} from '../types/errorMessages';
import {MicrosoftApiSetupData} from '../types/MicrosoftApiSetupData';
import {Source} from '../types/Source';
import {getRelatedSources} from '../utils/sourceUtils';

export interface SourceCredentials extends Record<Source, any> {
    [Source.PLAYSTATION_SALES]: {clientId: string; clientSecret: string};
    [Source.PLAYSTATION_WISHLIST_ACTIONS]: {clientId: string; clientSecret: string};
    [Source.NINTENDO_SALES]: {user: string; password: string; totpSecret?: string};
    [Source.GOG_SALES]: {user: string; password: string};
    [Source.META_QUEST_SALES]: {user: string; password: string; totpSecret?: string};
    [Source.META_RIFT_SALES]: {user: string; password: string; totpSecret?: string};
    [Source.HUMBLE_SALES]: {user: string; password: string; totpSecret?: string};
    [Source.STEAM_SALES]: {user: string; password: string; totpSecret?: string};
    [Source.STEAM_WISHLISTS]: {user: string; password: string; totpSecret?: string};
    [Source.STEAM_WISHLIST_BALANCE]: {user: string; password: string; totpSecret?: string};
    [Source.STEAM_IMPRESSIONS]: {user: string; password: string; totpSecret?: string};
    [Source.STEAM_DISCOUNTS]: {user: string; password: string; totpSecret?: string};
    [Source.MICROSOFT_SALES]: {user: string; password: string; apiSetupData: MicrosoftApiSetupData[]};
    [Source.EPIC_SALES]: {user: string; password: string; totpSecret?: string; loginWith: 'steam' | ''};
    [Source.GOOGLE_SALES]: {cloudStorageBucket: string; configJson: Record<string, string>};
}

export type Credentials = Record<string, any>;

type Values<T> = T[keyof T];

type SourceCredentialsParams = Values<{
    [P in keyof SourceCredentials]: [P, SourceCredentials[P]];
}>;

export type SourceAccountCredentials = Required<Pick<SourceAccount, 'cliParams'>>;

const extractCredentials = (credentials: Credentials | undefined, keys: string[]): Credentials => {
    if (!credentials) {
        return {};
    }
    return keys.reduce((result, key) => {
        result[key] = credentials[key];
        return result;
    }, {});
};

export function credentialsForSource(...[_source, credentials]: [Source, Credentials]): Credentials {
    return credentials;
}

export function credentialsToSourceAccount(...[_source, credentials]: SourceCredentialsParams): SourceAccountCredentials {
    return {cliParams: credentials};
}

export function sourceAccountToCredentials(...[source, {cliParams}]: [Source, SourceAccount]): Credentials | undefined {
    if (!cliParams || Object.keys(cliParams).length === 0) {
        return undefined;
    }
    if (getRelatedSources(Source.PLAYSTATION_SALES).includes(source)) {
        return extractCredentials(cliParams, ['clientId', 'clientSecret']);
    }

    if (source === Source.EPIC_SALES) {
        return extractCredentials(cliParams, ['user', 'password', 'totpSecret', 'loginWith']);
    }

    if (source === Source.MICROSOFT_SALES) {
        return extractCredentials(cliParams, ['user', 'password', 'apiSetupData']);
    }

    if (source === Source.GOOGLE_SALES) {
        return extractCredentials(cliParams, ['cloudStorageBucket', 'configJson']);
    }

    if (
        [
            ...getRelatedSources(Source.NINTENDO_SALES),
            ...getRelatedSources(Source.STEAM_SALES),
            ...getRelatedSources(Source.STEAM_IMPRESSIONS),
            ...getRelatedSources(Source.META_QUEST_SALES),
            Source.HUMBLE_SALES
        ].includes(source)
    ) {
        return extractCredentials(cliParams, ['user', 'password', 'totpSecret']);
    }

    return extractCredentials(cliParams, ['user', 'password']);
}

// TODO check types
export function sourceAccountToScrapeCredentials(...[source, {cliParams}]: [Source, SourceAccount]): Credentials | undefined {
    if (source === Source.MICROSOFT_SALES) {
        if (!cliParams) {
            throw new Error(MISSING_SCRAPE_CONFIGURATION_ERROR_MSG);
        }
        return extractCredentials(cliParams, ['apiSetupData']);
    }
}
