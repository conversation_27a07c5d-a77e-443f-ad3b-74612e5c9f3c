import * as telemetry from '../telemetry/telemetry';
import {Command, Source} from '../types';
import PriorityQueue from './PriorityQueue';
import {Run} from './Run';
import {ScrapeRun} from './ScrapeRun';

export class RunManager {
    private priorityQueue: PriorityQueue;
    private runsForProgressCalculation: Run[] = [];
    private readonly onScrapeProgress: (progress: number) => void;

    constructor(onScrapeProgress: (progress: number) => void, priorityQue: PriorityQueue) {
        this.priorityQueue = priorityQue;
        this.onScrapeProgress = onScrapeProgress;
    }

    public init() {
        this.priorityQueue.startInterval();
    }

    private updateRunsForProgressCalculation(run: Run) {
        const RunClass: new (...args: any[]) => Run = run.constructor as any;

        if (this.getProgressOfRunsOfType(RunClass) === 100) {
            /**
             * Why the isActive?
             * Because we determined that the progress of this particular run type is 100 (all jobs finished,
             * which means that progress calculation should be restarted.
             *
             * It is related to the fact that the priority queue has potentially NOT filtered active jobs just yet.
             */
            this.runsForProgressCalculation = this.priorityQueue
                .getQueue()
                .concat(this.priorityQueue.getCurrentlyRunning())
                .filter((run) => run instanceof RunClass && run.isActive());
        }

        if (run instanceof ScrapeRun && !run.context.isShadowRun) {
            run.setProgressListener((_localProgressPercentage: number) => {
                this.onScrapeProgress(this.scrapeProgress);
            });
        }

        if (!run.context.isShadowRun) {
            this.runsForProgressCalculation.push(run);
            this.onScrapeProgress(this.scrapeProgress); // to recalculate current progress after new run was added
        }
    }

    public isRunForSourceAlreadyRunning(source: Source) {
        return !!(this.getRunningRunForSource(source) || this.getScheduledRunForSource(source));
    }

    public async addRun(run: Run): Promise<void> {
        if (this.isRunForSourceAlreadyRunning(run.context.source) && !run.context.isShadowRun) {
            telemetry.trace('Cannot add multiple runs for the same source');
            return;
        }

        this.updateRunsForProgressCalculation(run);

        try {
            run.onScheduled && (await run.onScheduled());
            this.priorityQueue.add(run);
        } catch (error) {
            telemetry.trace('Run failed');
            telemetry.exception(error, true, {operation: 'run', source: run.context.source});
        }
    }

    async killAll() {
        await this.priorityQueue.killAll();
        this.runsForProgressCalculation = [];
    }

    getRunningRunForSource(source: Source, isShadowRun = false): Run | undefined {
        return this.priorityQueue.getCurrentlyRunning().find((run) => run.context.source === source && run.context.isShadowRun === isShadowRun && run.isActive());
    }

    getScheduledRunForSource(source: Source, isShadowRun = false): Run | undefined {
        return this.priorityQueue.getQueue().find((run) => run.context.source === source && run.context.isShadowRun === isShadowRun);
    }

    getRunningRuns() {
        return this.priorityQueue.getCurrentlyRunning();
    } //TODO not a big fan of this! Used only in "old" tests. We should think about getting rid of it
    getScheduledRuns() {
        return this.priorityQueue.getQueue();
    } //TODO not a big fan of this! Used only in "old" tests. We should think about getting rid of it

    async kill(source: Source, command: Command, isShadowRun = false): Promise<void> {
        await this.priorityQueue.killBySourceAndCommand(source, command, isShadowRun);
        this.runsForProgressCalculation = this.runsForProgressCalculation.filter((run) => run.context.source !== source || run.context.isShadowRun !== isShadowRun);
    }

    get scrapeProgress(): number {
        return this.getProgressOfRunsOfType(ScrapeRun, true);
    }

    /**
     * Design notes
     * This method has a side effect. It clears the runsForProgressCalculation array if all runs of given type are finished.
     * This operation is performed here rather than addRun method because it was causing confusion while debugging
     * ( even though all operations were finished there were still multiple jobs in the array until the next run was added )
     * and this operation is called more frequently
     */
    getProgressOfRunsOfType<T extends Run>(runClass: new (...args: any[]) => T, onlyNonShadowRuns = false): number {
        const runClassWithShadowOptionsFilter = (run) => run instanceof runClass && (!onlyNonShadowRuns || !run.context.isShadowRun);
        const runs = this.runsForProgressCalculation.filter(runClassWithShadowOptionsFilter);
        const atLeastOneRunIsStillActive = runs.find((run) => run.isActive());
        if (runs.length === 0) {
            return NaN;
        }

        const {totalItems, processedItems} = runs.reduce(
            (acc, run) => {
                acc.processedItems += run.processedItems;
                acc.totalItems += run.totalItems;
                return acc;
            },
            {
                totalItems: 0,
                processedItems: 0
            }
        );

        const progress = Math.round((processedItems / totalItems) * 100);

        if (progress === 100 || !atLeastOneRunIsStillActive) {
            this.runsForProgressCalculation = this.runsForProgressCalculation.filter((run) => !runClassWithShadowOptionsFilter(run));
        }
        return progress;
    }

    public async close() {
        await this.killAll();
        this.priorityQueue.stopInterval();
    }
}
