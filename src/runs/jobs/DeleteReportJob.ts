import {deleteReport} from '../../api/reportUpload';
import {ScrapeResult} from '../../processes/types';
import * as telemetry from '../../telemetry/telemetry';
import {ScrapeRunContext} from './context';
import {Job} from './Job';

/* eslint-disable @typescript-eslint/no-empty-function */

export class DeleteReportJob extends Job<void> {
    constructor(private mainDir: string) {
        super();
    }

    async execute({latestScrapeResults}: ScrapeRunContext): Promise<void> {
        if (latestScrapeResults === undefined) {
            return;
        }
        await Promise.all(
            latestScrapeResults.map(async (scrapeResult: ScrapeResult): Promise<void> => {
                await deleteReport(this.mainDir, scrapeResult);
            })
        );
    }

    async onFail(error: Error): Promise<void> {
        telemetry.trace('DeleteReportJob failed');
        telemetry.exception(error, false, {operation: 'DeleteReportJob'});
    }
}
