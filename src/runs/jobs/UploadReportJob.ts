import {sendReportUploadInfo, uploadReport} from '../../api/reportUpload';
import {ReportInfo} from '../../api/types';
import {ScrapeResult} from '../../processes/types';
import {iScraperServiceClient} from '../../telemetry/scraperService/iScraperServiceClient';
import * as telemetry from '../../telemetry/telemetry';
import {RetryActionOptions} from '../../utils/retryAction';
import {ScrapeRunContext} from './context';
import {Job} from './Job';

/* eslint-disable @typescript-eslint/no-empty-function */

export class UploadReportJob extends Job<void> {
    retryOptions: Omit<RetryActionOptions<any>, 'target'> = {
        maxAttempts: 3
    };
    private uploadResults: Array<{scrapeResult: ScrapeResult; reportInfo: ReportInfo}> = [];

    constructor(private mainDir: string, private s2Client: iScraperServiceClient) {
        super();
    }
    async execute({latestScrapeResults, isShadowRun}: ScrapeRunContext): Promise<void> {
        if (latestScrapeResults === undefined) {
            return;
        }
        this.uploadResults = [];
        await Promise.all(
            latestScrapeResults.map(async (scrapeResult: ScrapeResult): Promise<void> => {
                const uploadInfo = await uploadReport(this.mainDir, scrapeResult);
                if (isShadowRun) {
                    telemetry.trace('Skipping sendReportUploadInfo step, because this is a shadow run.');
                } else {
                    const reportInfo = await sendReportUploadInfo(uploadInfo);
                    this.uploadResults.push({scrapeResult, reportInfo});
                }
            })
        );
    }

    async onSuccess({operationId, source}: ScrapeRunContext): Promise<void> {
        for (const {scrapeResult, reportInfo} of this.uploadResults) {
            this.s2Client.scheduleReportUploadEvent({
                scrapeResult,
                reportInfo,
                operationId,
                source
            });
        }
    }

    async onFail(error: Error): Promise<void> {
        telemetry.trace('UploadReportJob failed');
        telemetry.exception(error, false, {operation: 'UploadReportJob'});
    }

    async kill(): Promise<void> {}
}
