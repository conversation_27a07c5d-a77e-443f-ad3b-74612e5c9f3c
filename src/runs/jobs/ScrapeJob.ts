import {ScraperConfigurationStatus} from '../../configurations/ScraperConfiguration';
import {BinaryProxy, ScrapeParams} from '../../dependencies/BinaryProxy';
import {ScrapeResult} from '../../processes/types';
import {Execution} from '../../processes/types/Execution';
import {Command} from '../../types';
import {sumDaysInDateRanges} from '../../utils/datesUtils';
import {UpdateLastScrapeDate, UpdateStatus} from '../status';
import {ScrapeRunContext} from './context';
import {Job} from './Job';

export class ScrapeJob extends Job<ScrapeResult[]> {
    private execution?: Execution<ScrapeResult[]>;

    constructor(private binaryProxy: BinaryProxy, private params: ScrapeParams, private updateStatus: UpdateStatus, private updateLastScrapeDate: UpdateLastScrapeDate) {
        super();
    }

    async execute(context: ScrapeRunContext): Promise<ScrapeResult[]> {
        this.execution = await this.binaryProxy.run<ScrapeResult[]>(Command.SCRAPE, this.params, context.operationId);
        context.latestScrapeResults = await this.execution.result; //TODO context should not be modified inside jobs. Jobs should return their results
        return context.latestScrapeResults;
    }

    async onSuccess({dateRangesToProcess, numberOfDaysToScrape}: ScrapeRunContext): Promise<void> {
        dateRangesToProcess.pop();
        const scrapedDays = numberOfDaysToScrape - sumDaysInDateRanges(dateRangesToProcess);

        // TODO: numberOfDaysToScrape could be pre-calculated in RunFactory
        await this.updateStatus(ScraperConfigurationStatus.RUNNING_SCRAPE, {scrapedDays, numberOfDaysToScrape});
        await this.updateLastScrapeDate(this.params.source);
    }

    async onFail(_error: Error) {
        // Handle failure
    }

    async kill(context: ScrapeRunContext): Promise<void> {
        await this.execution?.kill(context.operationId);
    }
}
