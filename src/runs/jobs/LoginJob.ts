import {errorType} from '../../configurations/errorType';
import {ScraperConfigurationStatus} from '../../configurations/ScraperConfiguration';
import {BinaryProxy, LoginParams} from '../../dependencies/BinaryProxy';
import {BinaryLoginResult} from '../../processes/types';
import {Execution} from '../../processes/types/Execution';
import * as telemetry from '../../telemetry/telemetry';
import {Command} from '../../types';
import {BaseContext} from './context';
import {Job, JobResult} from './Job';

export interface LoginJobContext extends BaseContext {
    sessionValid?: boolean;
    checkSessionResult?: BinaryLoginResult; // TODO: do we need it or should we rename this to loginResult?
}

export class LoginJob extends Job<BinaryLoginResult, LoginJobContext> {
    constructor(private binaryProxy: BinaryProxy, private loginParams: LoginParams) {
        super();
    }
    private execution?: Execution<BinaryLoginResult>;

    shouldRun(context: LoginJobContext, _previousResults: JobResult[]): boolean {
        return context.sessionValid == undefined || context.sessionValid == false;
    }

    async execute(context: LoginJobContext): Promise<BinaryLoginResult> {
        if (!this.loginParams.credentials) {
            const prefix = `Login[${context.operationId}]` + (context.shadowModeTask ? ` ShadowMode[${context.shadowModeTask.id}][${context.shadowModeTask.label}]` : '');
            telemetry.trace(`${prefix}: newStatus: ${ScraperConfigurationStatus.ERROR} additionalParams: ${JSON.stringify({errorType: errorType.SESSION_EXPIRED})}`);
            throw new Error('Session expired');
        }
        this.execution = await this.binaryProxy.run(Command.LOGIN, this.loginParams, context.operationId);
        context.checkSessionResult = await this.execution.result;
        context.sessionValid = true;
        return this.execution.result;
    }

    async kill(context: BaseContext): Promise<void> {
        await this.execution?.kill(context.operationId);
    }
}
