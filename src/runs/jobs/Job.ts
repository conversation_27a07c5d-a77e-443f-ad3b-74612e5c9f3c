/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-empty-function */
import {ScraperConfiguration} from '../../configurations/ScraperConfiguration';
import {BinaryResult} from '../../processes/types';
import {RetryActionOptions} from '../../utils/retryAction';
import {BaseContext} from './context';

export type LoginResult = ScraperConfiguration[];

export type JobResult = BinaryResult | LoginResult | void;

export abstract class Job<Result extends JobResult = any, Context extends BaseContext = BaseContext> {
    dependencies: Job<Result>[] = [];
    retryOptions: Omit<RetryActionOptions<any>, 'target'> = {maxAttempts: 1};

    abstract execute(context: Context): Promise<Result>;

    async onFail(error: Error, context: Context): Promise<void> {}

    async onSuccess(context: Context): Promise<void> {}

    async kill(context: Context): Promise<void> {}

    shouldRun(context: Context, previousResults: JobResult[]): boolean {
        return true;
    }
}
