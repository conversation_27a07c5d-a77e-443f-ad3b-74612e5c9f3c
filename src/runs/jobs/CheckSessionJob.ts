import {BinaryProxy, CheckSessionParams} from '../../dependencies/BinaryProxy';
import {CheckSessionResult} from '../../processes/types';
import {Execution} from '../../processes/types/Execution';
import * as telemetry from '../../telemetry/telemetry';
import {Command} from '../../types';
import {JobStoppedByUserError} from '../JobStoppedByUserError';
import {BaseContext} from './context';
import {Job} from './Job';

export interface CheckSessionJobContext extends BaseContext {
    sessionValid?: boolean;
    checkSessionResult?: CheckSessionResult;
}

export class CheckSessionJob extends Job<CheckSessionResult | undefined, CheckSessionJobContext> {
    private execution?: Execution<CheckSessionResult>;
    private isKilled = false;

    constructor(private binaryProxy: BinaryProxy, private checkSessionParams: CheckSessionParams) {
        super();
    }

    async execute(context: CheckSessionJobContext): Promise<CheckSessionResult | undefined> {
        try {
            this.execution = await this.binaryProxy.run<CheckSessionResult>(Command.CHECK_SESSION, this.checkSessionParams, context.operationId);
            const result = await this.execution.result;
            telemetry.trace(`Session is valid`);
            context.checkSessionResult = result;
            context.sessionValid = true;

            return result;
        } catch {
            if (this.isKilled) {
                telemetry.trace('Job was killed');
                throw new JobStoppedByUserError('CHECK_SESSION_EXECUTE_KILL');
            }

            telemetry.trace('Session is invalid');
            context.sessionValid = false;
        }
    }

    async kill(context: CheckSessionJobContext): Promise<void> {
        this.isKilled = true;
        await this.execution?.kill(context.operationId);
    }
}
