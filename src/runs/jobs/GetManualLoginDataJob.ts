import {BinaryProxy, CommonParams} from '../../dependencies/BinaryProxy';
import {ManualLoginDetailsResult} from '../../processes/types';
import {Execution} from '../../processes/types/Execution';
import {Command} from '../../types';
import {BaseContext} from './context';
import {Job} from './Job';

export class GetManualLoginDataJob extends Job<ManualLoginDetailsResult> {
    constructor(private binaryProxy: BinaryProxy, private runParams: CommonParams) {
        super();
    }
    private execution?: Execution<ManualLoginDetailsResult>;

    async execute(context: BaseContext): Promise<ManualLoginDetailsResult> {
        const execution = await this.binaryProxy.run<ManualLoginDetailsResult>(Command.GET_MANUAL_LOGIN_DETAILS, this.runParams, context.operationId);
        return execution.result;
    }
    async onSuccess(): Promise<void> {
        // Handle success
    }

    async onFail(): Promise<void> {
        // Handle failure
    }

    async kill(context: BaseContext): Promise<void> {
        await this.execution?.kill(context.operationId);
    }
}
