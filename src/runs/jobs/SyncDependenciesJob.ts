import {DependenciesManager} from '../../dependencies/DependenciesManager';
import {Job} from './Job';

export class SyncDependenciesJob extends Job<void> {
    constructor(private dependenciesManager: DependenciesManager) {
        super();
    }

    async execute(): Promise<void> {
        await this.dependenciesManager.syncDependencies(false, true);
    }

    async onSuccess(): Promise<void> {
        // Handle success
    }

    async onFail(): Promise<void> {
        // Handle failure
    }

    async kill(): Promise<void> {
        // Kill the job
    }
}
