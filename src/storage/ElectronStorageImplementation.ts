import {v4 as createUUID} from 'uuid';
import {
    EphemeralScraperConfigStates,
    RawScraperConfiguration,
    ScraperConfiguration,
    ScraperConfigurationAddParams,
    ScraperConfigurationEditParams,
    ScraperConfigurationStatus
} from '../configurations/ScraperConfiguration';
import {SourceAccount, SourceAccountAddParams, SourceAccountEditParams} from '../configurations/SourceAccount';
import {Schedule} from '../cron/Schedule';
import * as telemetry from '../telemetry/telemetry';
import {Source} from '../types';
import {removeFileOrDirectory} from '../utils/fileUtils';
import {getRelatedSources} from '../utils/sourceUtils';
import {Storage} from './Storage';

export interface ElectronStorageAdapter {
    set: <T>(key: string, value: T) => Promise<void>;
    get: <T>(key: string) => Promise<T | undefined>;
    delete: (key: string) => Promise<void>;
}

const configurationKey = (key?: string) => (key ? `configurations.${key}` : 'configurations');
const accountKey = (key?: string) => (key ? `accounts.${key}` : 'accounts');
const dailyScheduleKey = `dailyScheduler`;
const screenshotAndHtmlDumpDirPathKey = 'screenshotAndHtmlDumpDirPath';

/**
 * Console app remnants, leaving since the 'fake' identifier got probably populated quite a lot
 * @param accountIdentifier
 */
function sanitizeAccountIdentifier(accountIdentifier: string): string {
    const CONSOLE_APP_BROWSER_LOGIN_PLACEHOLDER = 'fake';
    return accountIdentifier === CONSOLE_APP_BROWSER_LOGIN_PLACEHOLDER ? 'Unidentified Session' : accountIdentifier;
}

const parseRawScraperConfiguration = (config?: RawScraperConfiguration): ScraperConfiguration | undefined => {
    return config
        ? {
              ...config,
              lastSuccessfulScrapeDate: config.lastSuccessfulScrapeDate ? new Date(config.lastSuccessfulScrapeDate) : undefined,
              createdAt: config.createdAt ? new Date(config.createdAt) : undefined
          }
        : undefined;
};
export class ElectronStorageImplementation implements Storage {
    constructor(private adapter: ElectronStorageAdapter) {}

    async getSourceAccount(sourceAccountId: string): Promise<SourceAccount | undefined> {
        if (!sourceAccountId) {
            //TODO when this is undefined it actually returns a value!? Should be investigated as Hans mentioned that it's made by design to return all accounts
            return;
        }
        const sourceAccount = await this.adapter.get<SourceAccount>(accountKey(sourceAccountId));
        if (sourceAccount) {
            return {...sourceAccount, accountIdentifier: sanitizeAccountIdentifier(sourceAccount.accountIdentifier)};
        }
        return;
    }

    async addSourceAccount(sourceAccount: SourceAccountAddParams): Promise<SourceAccount> {
        telemetry.trace(`Adding source account`);
        const sourceAccountToBeAdded = {...sourceAccount, id: createUUID()};
        await this.adapter.set(accountKey(sourceAccountToBeAdded.id), sourceAccountToBeAdded);
        return sourceAccountToBeAdded;
    }

    async editSourceAccount(sourceAccountEditPartial: SourceAccountEditParams): Promise<SourceAccount> {
        const existingSourceAccount = await this.getSourceAccount(sourceAccountEditPartial.id);
        if (!existingSourceAccount) {
            throw new Error(`Unable to edit SourceAccount with id ${sourceAccountEditPartial.id}, because account was not found`);
        }

        const attemptToChangeAccountIdentifier = sourceAccountEditPartial.accountIdentifier !== existingSourceAccount.accountIdentifier;
        const migrateToNewMicrosoft =
            existingSourceAccount.cliParams?.apiSetupData &&
            Array.isArray(existingSourceAccount.cliParams.apiSetupData) &&
            existingSourceAccount.cliParams.apiSetupData.some((setup) => 'index' in setup || 'shouldDownload' in setup);

        if (!migrateToNewMicrosoft && attemptToChangeAccountIdentifier) {
            throw new Error('Changing user identifier is not permitted');
        }

        const newSourceAccount: SourceAccount = {
            ...existingSourceAccount,
            ...sourceAccountEditPartial,
            cliParams: {...existingSourceAccount.cliParams, ...(sourceAccountEditPartial.cliParams ?? {})}
        };

        await this.adapter.set<SourceAccount>(accountKey(sourceAccountEditPartial.id), newSourceAccount);

        if (sourceAccountEditPartial.sessionPath && existingSourceAccount.sessionPath !== sourceAccountEditPartial.sessionPath) {
            await removeFileOrDirectory(existingSourceAccount.sessionPath);
        }

        return newSourceAccount;
    }

    async deleteSourceAccount(sourceAccountId: string): Promise<void> {
        const configs = await this.listScraperConfigurations();
        const configsLinkedToSourceAccount = configs.filter((config) => config.sourceAccountId === sourceAccountId);
        await Promise.all(configsLinkedToSourceAccount.map((config) => this.deleteScraperConfiguration(config.source)));
        await this.adapter.delete(accountKey(sourceAccountId));
    }

    async listSourceAccounts(): Promise<SourceAccount[]> {
        const accounts = await this.adapter.get<Record<string, SourceAccount>>(accountKey());
        return Object.values(accounts || []);
    }

    async addScraperConfiguration(scraperConfiguration: ScraperConfigurationAddParams, sourceAccount?: SourceAccountAddParams): Promise<ScraperConfiguration> {
        telemetry.trace(`Adding scraper configuration for source ${scraperConfiguration.source}`);
        if (!scraperConfiguration.sourceAccountId && !sourceAccount) {
            throw new Error('Unable to add Scraper configuration without sourceAccount');
        }

        const configToBeAdded = {
            ...scraperConfiguration,
            id: createUUID(),
            createdAt: new Date(),
            status: ScraperConfigurationStatus.CONFIGURED,
            sourceAccountId: scraperConfiguration.sourceAccountId ?? (await this.addSourceAccount(sourceAccount!)).id
        };

        await this.adapter.set(configurationKey(scraperConfiguration.source), configToBeAdded);
        return configToBeAdded;
    }

    async deleteScraperConfiguration(source: Source): Promise<void> {
        await this.adapter.delete(configurationKey(source));
    }

    async listScraperConfigurations(): Promise<ScraperConfiguration[]> {
        const rawConfigs = await this.adapter.get<Record<string, RawScraperConfiguration>>(configurationKey());
        const configs: Record<string, ScraperConfiguration> = {};
        for (const key in rawConfigs) {
            configs[key] = parseRawScraperConfiguration(rawConfigs[key])!;
        }
        return Object.values(configs ?? {});
    }

    async getRawScraperConfigurationBySource(source: Source): Promise<RawScraperConfiguration | undefined> {
        return this.adapter.get<RawScraperConfiguration>(configurationKey(source));
    }

    async getScraperConfigurationBySource(
        source: Source,
        shouldCreateBasedOnRelatedSources?: boolean,
        saveConfiguration?: boolean
    ): Promise<ScraperConfiguration | undefined> {
        const rawConfig = await this.getRawScraperConfigurationBySource(source);

        if (rawConfig || !shouldCreateBasedOnRelatedSources) {
            return parseRawScraperConfiguration(rawConfig);
        }

        const relatedSources = getRelatedSources(source).filter((relatedSource) => relatedSource != source);
        for (const relatedSource of relatedSources) {
            const relatedConfig = await this.getRawScraperConfigurationBySource(relatedSource);
            if (relatedConfig) {
                telemetry.trace(`Adding ${saveConfiguration ? '' : 'temporary'} configuration for ${source} based on ${relatedSource}`);
                if (saveConfiguration) {
                    return this.addScraperConfiguration({source, sourceAccountId: relatedConfig.sourceAccountId});
                } else {
                    return {
                        source,
                        sourceAccountId: relatedConfig.sourceAccountId,
                        status: ScraperConfigurationStatus.CONFIGURED,
                        id: `TEMPORARY_MADE_FROM_${relatedConfig.id}`,
                        createdAt: relatedConfig.createdAt ? new Date(relatedConfig.createdAt) : undefined
                    };
                }
            }
        }
    }

    async editScraperConfiguration(scraperConfiguration: ScraperConfigurationEditParams): Promise<ScraperConfiguration> {
        telemetry.trace(`Editing scraper configuration ${scraperConfiguration.source}`);

        const rawConfig = await this.getRawScraperConfigurationBySource(scraperConfiguration.source);
        const config = parseRawScraperConfiguration(rawConfig);

        if (!config) {
            throw new Error(`Unable to edit ScraperConfiguration with source ${scraperConfiguration.source}, because configuration was not found`);
        }
        if (scraperConfiguration.status && EphemeralScraperConfigStates.includes(scraperConfiguration.status)) {
            throw new Error(`${scraperConfiguration.status} state is ephemeral and should not be saved!`);
        }
        const updatedConfig: ScraperConfiguration = {...config, ...scraperConfiguration};

        for (const key of Object.keys(updatedConfig)) {
            if (updatedConfig[key] === undefined) {
                delete updatedConfig[key];
            }
        }
        await this.adapter.set(configurationKey(scraperConfiguration.source), updatedConfig);

        return updatedConfig;
    }

    async saveDailyScheduler(schedules: Schedule[]): Promise<Schedule[]> {
        await this.adapter.set<Schedule[]>(dailyScheduleKey, schedules);
        return schedules;
    }

    async getDailyScheduler(): Promise<Schedule[] | undefined> {
        const schedules = await this.adapter.get<Schedule[] | Schedule | undefined>(dailyScheduleKey);
        if (schedules && !Array.isArray(schedules)) {
            return [schedules];
        }
        return schedules;
    }

    async saveScreenshotAndHtmlDumpDirPath(path: string | undefined): Promise<void> {
        if (typeof path === 'string') {
            await this.adapter.set(screenshotAndHtmlDumpDirPathKey, path);
        } else {
            await this.adapter.delete(screenshotAndHtmlDumpDirPathKey);
        }
    }

    async getScreenshotAndHtmlDumpDirPath(): Promise<string | undefined> {
        return this.adapter.get<string | undefined>(screenshotAndHtmlDumpDirPathKey);
    }
}
