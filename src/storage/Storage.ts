import {ScraperConfiguration, ScraperConfigurationAddParams, ScraperConfigurationEditParams} from '../configurations/ScraperConfiguration';
import {SourceAccount, SourceAccountAddParams, SourceAccountEditParams} from '../configurations/SourceAccount';
import {Schedule} from '../cron/Schedule';
import {Source} from '../types/Source';

export interface Storage {
    getSourceAccount(sourceAccountId: string): Promise<SourceAccount | undefined>;

    addSourceAccount(sourceAccount: SourceAccountAddParams): Promise<SourceAccount>;

    editSourceAccount(sourceAccount: SourceAccountEditParams): Promise<SourceAccount>;

    deleteSourceAccount(sourceAccountId: string): Promise<void>;

    listSourceAccounts(): Promise<SourceAccount[]>;

    addScraperConfiguration(scraperConfiguration: ScraperConfigurationAddParams, sourceAccount?: SourceAccountAddParams): Promise<ScraperConfiguration>;

    deleteScraperConfiguration(source: Source): Promise<void>;

    listScraperConfigurations(): Promise<ScraperConfiguration[]>;

    editScraperConfiguration(scraperConfiguration: ScraperConfigurationEditParams): Promise<ScraperConfiguration>;

    getScraperConfigurationBySource(source: Source, shouldCreateBasedOnRelatedSources?: boolean, saveConfiguration?: boolean): Promise<ScraperConfiguration | undefined>;

    saveDailyScheduler(schedules: Schedule[]): Promise<Schedule[]>;

    getDailyScheduler(): Promise<Schedule[] | undefined>;

    saveScreenshotAndHtmlDumpDirPath(screenshotAndDumpDirPath: string | undefined): Promise<void>;

    getScreenshotAndHtmlDumpDirPath(): Promise<string | undefined>;
}
