import {ErrorType} from '../../configurations/errorType';
import {ScraperConfiguration} from '../../configurations/ScraperConfiguration';
import {Storage} from '../Storage';

const oldToNewMapping: Record<string, {status: string; errorType?: ErrorType}> = {
    VALID: {status: 'CONFIGURED'},
    NEW: {status: 'CONFIGURED'},
    RUNNING_SCRAPE: {status: 'RUNNING_SCRAPE'},
    SCHEDULED: {status: 'SCHEDULED'},
    ERROR: {status: 'ERROR'},
    MANUAL_SESSION_EXPIRED: {status: 'ERROR', errorType: 'SESSION_EXPIRED'},
    NEEDS_ACTION: {status: 'ERROR'}
};

export async function runMigration(storage: Storage) {
    const configs = await storage.listScraperConfigurations();
    await Promise.all(configs.map((config) => convertToNewStatus(storage, config)));
}

export async function revertMigration(storage: Storage) {
    const configs = await storage.listScraperConfigurations();
    await Promise.all(configs.map((config) => convertToOldStatus(storage, config)));
}

async function convertToNewStatus(storage: Storage, config: ScraperConfiguration) {
    const currentStatus = config.status;
    const mapping = oldToNewMapping[currentStatus];
    if (mapping) {
        const {status: newStatus, errorType} = oldToNewMapping[currentStatus];
        if (currentStatus !== newStatus) {
            await storage.editScraperConfiguration({
                source: config.source,
                status: newStatus as any,
                errorType
            });
        }
    }
}

async function convertToOldStatus(storage: Storage, config: ScraperConfiguration) {
    const currentStatus = config.status;
    const currentErrorType = config.errorType;
    const mapping =
        Object.entries(oldToNewMapping).find(([_oldStatus, {status, errorType}]) => status === currentStatus && errorType === currentErrorType) ||
        Object.entries(oldToNewMapping).find(([_oldStatus, {status}]) => status === currentStatus);
    if (mapping) {
        const oldStatus = mapping[0];
        if (currentStatus !== oldStatus || currentErrorType !== undefined) {
            await storage.editScraperConfiguration({
                source: config.source,
                status: oldStatus as any,
                errorType: undefined
            });
        }
    }
}
