/* eslint-disable import/order */
import * as path from 'path';
import {version} from '../package.json';
import {
    addAuthenticationHeaders as authToApiWithJWT,
    getNewSourcesFromAPI,
    getScrapDates,
    getShadowModeTask,
    logoutFromApi,
    send2faCode,
    setupApi
} from './api/ScraperApi';
import removeUnusedSessionFiles from './configurations/removeUnusedSessionFiles';
import {ScraperConfiguration, ScraperConfigurationAddParams, ScraperConfigurationEditParams, ScraperConfigurationStatus} from './configurations/ScraperConfiguration';
import {ScraperConfigurationManager} from './configurations/ScraperConfigurationManager';
import {convertToPublicScraperConfiguration} from './configurations/ScraperConfigurationUtils';
import {closeSentry, initSentry, withSentryErrorHandling} from './telemetry/sentry';
/* eslint-enable import/order */
import {SourceAccount, SourceAccountAddParams} from './configurations/SourceAccount';
import {isNextScheduleTomorrow, startDailyCron, stopDailyCron} from './cron/dailyScrapeCron';
import {validateSchedule} from './cron/paramsValidation';
import {DEFAULT_DAILY_SCHEDULE, Schedule} from './cron/Schedule';
import {BinaryProxy} from './dependencies/BinaryProxy';
import {DependenciesManager} from './dependencies/DependenciesManager';
import {Emitter, ScraperLibEmitter} from './emitter';
import {FeatureFlag, isFeatureEnabled, setFeatureFlags} from './featureFlags/featureFlags';
import {killAllProcesses} from './processes/processManager';
import {CheckSessionResult, ManualLoginDetailsResult, SourceSideOrganization} from './processes/types';
import {AwaitableJob} from './publicInterfaces/AwaitableJob';
import {PublicScraperConfiguration} from './publicInterfaces/PublicScraperConfiguration';
import {Credentials, credentialsToSourceAccount} from './runs/credentials';
import {ScrapeRunContext} from './runs/jobs/context';
import PriorityQueue from './runs/PriorityQueue';
import {Run} from './runs/Run';
import {RunFactory} from './runs/RunFactory';
import {RunManager} from './runs/RunManager';
import {ElectronStorageAdapter, ElectronStorageImplementation} from './storage/ElectronStorageImplementation';
import {runMigration} from './storage/migrations/statuses';
import {Storage} from './storage/Storage';
import {HttpScraperServiceClient} from './telemetry/scraperService/httpScraperServiceClient';
import {ScraperServiceLogLevel, iScraperServiceClient} from './telemetry/scraperService/iScraperServiceClient';
import {Outbox} from './telemetry/scraperService/outbox';
import {TriggeredBy} from './telemetry/scraperService/scraperServiceEvents';
import * as telemetry from './telemetry/telemetry';
import {Command, ShadowModeTask, Source} from './types';
import {STORAGE_MIGRATIONS_ERROR_MSG} from './types/errorMessages';
import {eventTypes} from './types/EventsTypes';
import {MicrosoftApiSetupData} from './types/MicrosoftApiSetupData';
import {removeFileOrDirectory} from './utils/fileUtils';
import {buildSessionPath} from './utils/session';
import {getRelatedSources} from './utils/sourceUtils';

type StorageOptions = {type: 'json'; adapter: ElectronStorageAdapter};
const defaultShadowModeInterval = 60000;

interface ScraperLibProperties {
    apiUrl: string;
    scraperServiceUrl: string;
    storageOptions: StorageOptions;
    apiToken: string;
    mainDir?: string;
    emitter?: ScraperLibEmitter;
    sentryDSN?: string;
    scraperServiceClient?: iScraperServiceClient;
    runManagerPriorityQue?: PriorityQueue;
}

interface InitOptions {
    /**
     * If true, the app will sync all dependencies on startup
     */
    syncBinsOnInit?: boolean;
    cron?: {
        /**
         * If true, the app will start the daily cron job to scrape all sources
         */
        start: boolean;
        /**
         * If true, the app will check if there was a missed scheduled run for today and run scrapeAllSources
         */
        forceMissedScheduledRuns: boolean;
    };
    /**
     * Path to a directory with chromium binaries that will be extracted and placed in scrapers dir to speed up download
     */
    preProvidedBinariesDir?: string;
    shadowMode?: {
        /**
         * Will activate shadow mode when starting the scraper lib allowing for remote debugging
         */
        active: boolean;
        /**
         * Interval in ms for checking if there is a shadow task to run
         */
        interval?: number;
    };
    /**
     * If true, the app will delete all session files that are not used by any source account
     */
    deleteUnusedSessionFiles?: boolean;
    /**
     * Should the app try to load the unsent telemetry dump file on startup and attempt to send any messages saved there.
     */
    loadOutboxDumpFile?: boolean;
}

const scraperNotFoundText = 'Scraper configuration not found for';

/**
 * Main integration point between the `integrated service` and `Scrapers`.
 * Should encapsulate all `non-integration` specific business logic.
 */
export class ScraperLib {
    public readonly mainDir: string;
    private readonly dependenciesManager: DependenciesManager;
    private readonly binaryProxy: BinaryProxy;
    private readonly storage: Storage;
    private shadowModeTimer: NodeJS.Timer | undefined;
    private readonly runManager: RunManager;
    private runFactory: RunFactory;
    private readonly scraperServiceClient: iScraperServiceClient;
    private readonly scraperConfigurationManager: ScraperConfigurationManager;

    public constructor({
        storageOptions,
        mainDir,
        emitter,
        apiUrl,
        apiToken,
        scraperServiceUrl,
        sentryDSN,
        scraperServiceClient,
        runManagerPriorityQue
    }: ScraperLibProperties) {
        this.mainDir = path.resolve(mainDir ?? '.');
        if (sentryDSN) {
            initSentry(sentryDSN);
        }
        if (scraperServiceClient) {
            this.scraperServiceClient = scraperServiceClient;
        } else {
            this.scraperServiceClient = new HttpScraperServiceClient(
                scraperServiceUrl,
                apiToken,
                `ScraperLib_${version}`,
                new Outbox(path.join(this.mainDir, 'outbox.ndjson'))
            );
        }
        telemetry.setS2Client(this.scraperServiceClient);

        telemetry.trace('Creating scraperLib instance');
        if (storageOptions.type === 'json') {
            this.storage = new ElectronStorageImplementation(storageOptions.adapter);
        }

        if (emitter) {
            Emitter.setEmitter(emitter);
        }

        setupApi(apiUrl, apiToken);

        this.dependenciesManager = new DependenciesManager(this.mainDir);
        this.binaryProxy = new BinaryProxy(this.mainDir, this.dependenciesManager);

        this.scraperConfigurationManager = new ScraperConfigurationManager(this.storage, this.scraperServiceClient);

        this.runManager = new RunManager(
            (progress) => {
                Emitter.emit({type: eventTypes.progress, progress});
            },
            runManagerPriorityQue ? runManagerPriorityQue : new PriorityQueue(5)
        );

        this.runFactory = new RunFactory(
            this.mainDir,
            this.storage,

            this.dependenciesManager,
            this.binaryProxy,
            this.scraperServiceClient,
            this.scraperConfigurationManager
        );
    }

    /**
     * Terminate all connections to external systems
     */
    @withSentryErrorHandling()
    public async close() {
        this.stopShadowModeTaskListener();

        await this.runManager.close();

        telemetry.trace('Closing API connection');
        logoutFromApi();

        telemetry.trace('Stop daily cron connection');
        await stopDailyCron();

        telemetry.trace('Stop all scrapers');
        await this.stopAllScrapes();

        telemetry.trace('Stop all running processes');
        await killAllProcesses();

        // This should be the last steps, so we do not lose any data
        telemetry.trace('Closing ScraperServiceClient');
        await this.scraperServiceClient.close();

        telemetry.trace('closing sentry connection');
        await closeSentry();
    }

    /**
     * All external and internal system are initialized in the init function (except for Scraper-API which is initialized in the constructor).
     * In order to properly use a ScraperLib instance it needs to be initiated.
     * This function should be called only once per instance.
     * At the time of writing the electron-app creates only a single scraperLib instance and closes/initializes it on logout/login.
     */
    @withSentryErrorHandling()
    public async init({syncBinsOnInit, cron, preProvidedBinariesDir, shadowMode, deleteUnusedSessionFiles, loadOutboxDumpFile}: InitOptions = {}) {
        // This should be the first step, so we do not lose any data and setup communication properly!
        if (loadOutboxDumpFile) {
            await this.scraperServiceClient.loadOutboxFromFile();
        }

        await this.scraperServiceClient.loadIpInfo();
        this.scraperServiceClient.startSendingOutbox();

        telemetry.trace('Initializing ScraperLib');

        this.runManager.init();
        await this.runStorageMigrations();
        await this.scraperConfigurationManager.createMissingScraperConfigurationsBasedOnRelatedSources();

        if (preProvidedBinariesDir) {
            await this.dependenciesManager.movePreProvidedChromium(preProvidedBinariesDir);
        }

        if (syncBinsOnInit) {
            await this.dependenciesManager.syncDependencies(true);
        }

        if (cron?.start) {
            telemetry.trace('Start daily cron job');
            await this.startDailyScrapeCron();
        }

        if (cron?.forceMissedScheduledRuns) {
            if (await this.wasTodayScheduledRunMissed()) {
                void this.scrapeAllSources();
            }
        }

        //This should run prior to shadow mode task listener activation since it will delete shadow mode session files!
        if (deleteUnusedSessionFiles) {
            await removeUnusedSessionFiles(this.storage, this.mainDir);
        }

        if (shadowMode?.active) {
            this.activateShadowModeTaskListener(shadowMode.interval || defaultShadowModeInterval);
        }

        Emitter.emit({type: eventTypes.initialized, message: 'ScraperLib initialized'});
    }

    private activateShadowModeTaskListener(interval: number) {
        this.stopShadowModeTaskListener();
        this.shadowModeTimer = setInterval(async () => {
            if (isFeatureEnabled('shadow-mode-enabled')) {
                telemetry.trace('Checking for shadow task');
                const task = await getShadowModeTask();
                if (task) {
                    await this.runShadowModeTask(task);
                }
            }
        }, interval);
    }

    private stopShadowModeTaskListener() {
        if (this.shadowModeTimer) {
            telemetry.trace('Stopping shadow mode task listener');
            clearInterval(this.shadowModeTimer);
            this.shadowModeTimer = undefined;
        }
    }

    /**
     * Despite being a private function, we want to wrap this one in telemetry to have the full context of a shadow run in the logs!
     * @param task
     */

    @withSentryErrorHandling()
    private async runShadowModeTask(task: ShadowModeTask): Promise<AwaitableJob | undefined> {
        telemetry.trace(`Shadow task command: ${task.command}`);
        const sourceConfiguration = await this.scraperConfigurationManager.getScraperConfigurationBySource(task.source, true, false);
        if (!sourceConfiguration) {
            telemetry.trace('No proper configuration found', {logLevel: ScraperServiceLogLevel.ERROR});
            return;
        }
        let run: Run<any>;
        switch (task.command) {
            case Command.SCRAPE: {
                run = await this.runFactory.createScrapeRun(task.source, task.dateRanges, sourceConfiguration, TriggeredBy.SHADOW_TASK, task);
                break;
            }
            case Command.LOGIN: {
                run = await this.runFactory.createShadowModeLoginWithCredentialsRun(task.source, sourceConfiguration, task);
                break;
            }
            default: {
                telemetry.exception(new Error(`Command ${(task as any).command} is not supported`), false, {response: task});
                return;
            }
        }

        await this.runManager.addRun(run);
        telemetry.trace(`Shadow task ${task.id} execution finished`);
        return {jobFinishedPromise: run.jobFinishedPromise.promise};
    }

    /**
     * Set JWT for communication with Scraper-API
     *
     * @param jwt - JWT generated by API
     */

    @withSentryErrorHandling()
    public async updateJWT(jwt: string): Promise<string | void> {
        authToApiWithJWT(jwt);
        this.scraperServiceClient.updateToken(jwt);
    }

    /**
     * Close the current connection to the Scraper-API.
     */

    @withSentryErrorHandling()
    public async logoutFromApi(): Promise<void> {
        telemetry.trace(`Logging out from the API`);
        logoutFromApi();
    }

    /**
     * Send 2FA code to the Scraper-API.
     */

    @withSentryErrorHandling()
    public async send2faForSource(source: Source, code: string): Promise<void> {
        telemetry.trace(`Sending 2FA for ${source}`);
        await send2faCode(source, code);
    }

    /**
     * Setup time for daily cron job.
     */

    @withSentryErrorHandling()
    public async setupDailyCron(schedules: Schedule[]): Promise<void> {
        telemetry.trace(`Setup daily cron to ${schedules.map((s) => `${s.hours}:${s.minutes}`).join(', ')}`);
        schedules.forEach(validateSchedule);
        await this.storage.saveDailyScheduler(schedules);
        await this.startDailyScrapeCron();
    }

    /**
     * Get daily cron schedule.
     */

    @withSentryErrorHandling()
    public async getDailyCronSchedule(): Promise<Schedule[]> {
        return (await this.storage.getDailyScheduler()) ?? [DEFAULT_DAILY_SCHEDULE];
    }

    /**
     * Scrape the specified source.
     * @param source
     * @param triggeredBy
     * @param includeHotStorage - if provided then even if source is fully up-to-date the hot storage period will be scraped again
     *
     * //TODO type not used in electron, only in acceptance tests. It exposes too much of internal operations
     *
     */

    @withSentryErrorHandling()
    public async scrapeSource(source: Source, includeHotStorage?: boolean, triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON): Promise<AwaitableJob | undefined> {
        const run: Run<ScrapeRunContext> | undefined = await this.scrapeSingleSource(source, includeHotStorage, triggeredBy);
        if (!run) {
            return undefined;
        } else {
            return {jobFinishedPromise: run.jobFinishedPromise.promise};
        }
    }

    /**
     * Scrape all configured sources
     */

    @withSentryErrorHandling()
    public async scrapeAllSources(includeHotStorage?: boolean, triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON): Promise<AwaitableJob[]> {
        telemetry.trace(`Starting scrape all sources`);
        const allConfigs = await this.nonTelemetryWrappedListScraperConfigurations();
        const sources = allConfigs.map(({source}) => source);

        const jobs: AwaitableJob[] = [];
        for (const source of sources) {
            if (!this.runManager.isRunForSourceAlreadyRunning(source)) {
                const job = await this.scrapeSingleSource(source, includeHotStorage, triggeredBy);
                if (job) {
                    jobs.push({jobFinishedPromise: job.jobFinishedPromise.promise});
                }
            }
        }

        return jobs;
    }

    private async scrapeSingleSource(
        source: Source,
        includeHotStorage: undefined | boolean,
        triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON
    ): Promise<Run<ScrapeRunContext> | undefined> {
        telemetry.trace(`Starting scrape source: ${source}`);

        const dateRanges = await getScrapDates(source, includeHotStorage);
        if (!dateRanges.length) {
            telemetry.trace('No date ranges to scrape');
        }
        telemetry.trace(`dateRanges: ${JSON.stringify(dateRanges)}`);

        const scraperConfiguration = await this.scraperConfigurationManager.getScraperConfigurationBySource(source, true, true);
        if (!scraperConfiguration) {
            throw new Error(`Scraper configuration for source ${source} not found`);
        }

        if (scraperConfiguration.status === ScraperConfigurationStatus.DISABLED) {
            telemetry.trace(`${source} is disabled, scrape skipped`);
            return;
        }

        if (!dateRanges.length) {
            const updatedConfig = await this.scraperConfigurationManager.editScraperConfiguration({
                source,
                status: ScraperConfigurationStatus.CONFIGURED,
                lastSuccessfulScrapeDate: new Date()
            });
            Emitter.emit({
                type: eventTypes.scraperRunUpdate,
                message: 'Source is up to date',
                source,
                newStatus: updatedConfig.status
            });
            telemetry.trace(`Source: ${source} is up to date`);
            return;
        }

        const scrapeRun = await this.runFactory.createScrapeRun(source, dateRanges, scraperConfiguration, triggeredBy);
        await this.runManager.addRun(scrapeRun);
        return scrapeRun;
    }

    /**
     * Log in to the specified source without storing credentials.
     * @param source
     * @param triggeredBy
     * @param credentials
     */

    @withSentryErrorHandling()
    public async checkCredentials(source: Source, credentials: any, triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON): Promise<{result: CheckSessionResult}> {
        telemetry.trace(`Checking credentials to source ${source}`);

        const sessionPath = buildSessionPath(this.mainDir, source);
        const run = await this.runFactory.createLoginWithCredentialsRun(source, credentials, sessionPath, triggeredBy);

        await this.runManager.addRun(run);
        const [_, loginJobResult] = await run.getJobResults();

        await removeFileOrDirectory(sessionPath);

        return {result: loginJobResult};
    }

    /**
     * Log in to the specified source with the provided SourceAccount. A session file will be created in the main directory.
     * @param source
     * @param triggeredBy
     * @param credentials
     */

    @withSentryErrorHandling()
    public async loginWithCredentials(source: Source, credentials: any, triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON): Promise<ScraperConfiguration[]> {
        telemetry.trace(`Logging in with credentials to source ${source}`);

        const sessionPath = buildSessionPath(this.mainDir, source);
        const run = await this.runFactory.createLoginWithCredentialsRun(source, credentials, sessionPath, triggeredBy);

        await this.runManager.addRun(run);
        const [_, loginJobResult] = await run.getJobResults();

        return this.scraperConfigurationManager.addSourceAccountAndHandleRelatedConfigs(source, {
            ...credentialsToSourceAccount(source, credentials),
            sessionPath,
            accountIdentifier: loginJobResult.id
        });
    }

    @withSentryErrorHandling()
    public async loginWithCookies(source: Source, cookies: any[], triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON): Promise<ScraperConfiguration[]> {
        try {
            const loginWithCookiesRun = await this.runFactory.createLoginWithCookiesRun(source, triggeredBy, cookies);
            await this.runManager.addRun(loginWithCookiesRun);
            const results = await loginWithCookiesRun.getJobResults();

            const scraperConfigurationsRunResult = results[results.length - 1] as unknown as ScraperConfiguration[];
            return scraperConfigurationsRunResult.map((config) => convertToPublicScraperConfiguration(config, this.runManager));
        } catch (e) {
            telemetry.exception(e);
            throw new Error(`Provided session doesn't allow to scrape`);
        }
    }

    /**
     * Obtain manual login data for this specific source.
     *
     * @param triggeredBy
     * @param source
     */

    @withSentryErrorHandling()
    public async getManualLoginData(source: Source, triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON): Promise<{result: ManualLoginDetailsResult}> {
        telemetry.trace(`Obtaining manual login data for source: ${source}`);
        const run = await this.runFactory.createGetManualLoginDataRun(source, triggeredBy);
        await this.runManager.addRun(run);
        const [jobResults] = await run.getJobResults();
        return {result: jobResults};
    }

    /**
     * Immediately stop all active scrapings regardless of their status/advancement.
     */

    @withSentryErrorHandling()
    public async stopAllScrapes(): Promise<void> {
        telemetry.trace('Stopping all scrapes');
        await this.runManager.killAll();
    }

    /**
     * Immediately stop a specific active scraping regardless of its status/advancement.
     */

    @withSentryErrorHandling()
    public async stopScrape(source: Source): Promise<void> {
        telemetry.trace(`Stopping scrape for source: ${source}`);
        await this.runManager.kill(source, Command.SCRAPE);
    }

    /**
     * Immediately stop a specific active login process regardless of its status/advancement.
     */

    @withSentryErrorHandling()
    public async stopLogin(source: Source): Promise<void> {
        telemetry.trace(`Stopping login for source: ${source}`);
        await this.runManager.kill(source, Command.LOGIN);
    }

    /**
     * Disable source config so it won't be triggered by scrapeAllSources or cron job
     */

    @withSentryErrorHandling()
    public async disableScraperConfiguration(source: Source): Promise<ScraperConfiguration> {
        telemetry.trace(`Disabling scraper configuration for source: ${source}`);
        return this.scraperConfigurationManager.disableScraperConfiguration(source);
    }

    /**
     * Enable source config so it becomes active again.
     */
    @withSentryErrorHandling()
    public async enableScraperConfiguration(source: Source): Promise<ScraperConfiguration> {
        telemetry.trace(`Enabling scraper configuration for source: ${source}`);
        return this.scraperConfigurationManager.enableScraperConfiguration(source);
    }

    @withSentryErrorHandling()
    public async getSourceAccount(sourceAccountId: string): Promise<SourceAccount | undefined> {
        telemetry.trace(`Getting source account ${sourceAccountId}`);
        return this.scraperConfigurationManager.getSourceAccount(sourceAccountId);
    }

    @withSentryErrorHandling()
    public async getSourceAccountBySource(source: Source): Promise<SourceAccount | undefined> {
        telemetry.trace(`Getting source account by source: ${source}`);
        const config = await this.scraperConfigurationManager.getScraperConfigurationBySource(source);
        return config?.sourceAccountId ? this.scraperConfigurationManager.getSourceAccount(config.sourceAccountId) : undefined;
    }

    @withSentryErrorHandling()
    public async getSourcesBySourceAccountId(sourceAccountId: string): Promise<Source[]> {
        telemetry.trace(`Getting sources for source account ${sourceAccountId}`);
        const configs = await this.scraperConfigurationManager.getScraperConfigurations();
        return configs.filter((config) => config.sourceAccountId === sourceAccountId).map((config) => config.source);
    }

    @withSentryErrorHandling()
    public async setCredentialsForSourceAccount(
        //TODO move to scraperConfigurationManager or maybe add a ScraperAccountManager?
        sourceAccountId: string,
        credentials: Credentials,
        triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON
    ): Promise<SourceAccount> {
        telemetry.trace(`Setting credentials for source account ${sourceAccountId}`);
        const sourceAccount = await this.getSourceAccount(sourceAccountId);
        if (!sourceAccount) {
            telemetry.logAndThrowError(`Source account not found: ${sourceAccountId}`, {sourceAccountId});
        }
        const configs = await this.scraperConfigurationManager.getScraperConfigurations();
        const configsLinkedToSourceAccount = configs.filter((config) => config.sourceAccountId === sourceAccountId);

        if (configsLinkedToSourceAccount.length == 0) {
            telemetry.logAndThrowError(`Source account is not linked to any scraper configuration: ${sourceAccountId}`, {sourceAccountId});
        }
        const source = configsLinkedToSourceAccount[0].source;

        const sessionPath = buildSessionPath(this.mainDir, source);

        const run = await this.runFactory.createLoginWithCredentialsRun(source, credentials, sessionPath, triggeredBy);

        await this.runManager.addRun(run);
        const [_, loginJobResult] = await run.getJobResults();

        return this.scraperConfigurationManager.editSourceAccount({
            id: sourceAccountId,
            cliParams: credentials,
            accountIdentifier: loginJobResult.id,
            sessionPath
        });
    }

    /**
     * Should return all available scraper configurations and additional data about the status of any 'running'/'last ran' scrapers.
     */

    @withSentryErrorHandling()
    public async listScraperConfigurations(): Promise<PublicScraperConfiguration[]> {
        return this.nonTelemetryWrappedListScraperConfigurations();
    }

    private async nonTelemetryWrappedListScraperConfigurations(): Promise<PublicScraperConfiguration[]> {
        return (await this.scraperConfigurationManager.getScraperConfigurations()).map((config) => convertToPublicScraperConfiguration(config, this.runManager));
    }

    @withSentryErrorHandling()
    public async addScraperConfiguration(config: ScraperConfigurationAddParams, sourceAccount?: SourceAccountAddParams): Promise<ScraperConfiguration> {
        return this.scraperConfigurationManager.addScraperConfiguration(config, sourceAccount);
    }

    @withSentryErrorHandling()
    public async getScraperConfiguration(source: Source): Promise<ScraperConfiguration | undefined> {
        telemetry.trace(`Getting scraper configuration for source ${source}`);
        return this.scraperConfigurationManager.getScraperConfiguration(source);
    }

    @withSentryErrorHandling()
    public async editScraperConfiguration(scraperConfiguration: ScraperConfigurationEditParams): Promise<ScraperConfiguration> {
        return this.scraperConfigurationManager.editScraperConfiguration(scraperConfiguration);
    }

    @withSentryErrorHandling()
    public async addSkuToIgnoreForConfiguration(source: Source, skuToIgnore: string[]): Promise<ScraperConfiguration> {
        telemetry.trace(`Adding skus to ignore for ${source}`);
        return this.scraperConfigurationManager.editScraperConfiguration({source, skuToIgnore});
    }

    @withSentryErrorHandling()
    public async getSourceSideOrganizations(source: Source): Promise<{result: SourceSideOrganization[]}> {
        telemetry.trace(`Running getSourceSideOrganizations for: ${source}`);
        const config = await this.scraperConfigurationManager.getScraperConfigurationBySource(source);
        if (!config) {
            telemetry.logAndThrowError(`${scraperNotFoundText} ${source}`, {source});
        }

        const run = await this.runFactory.createGetSourceSideOrganizationsRun(config);
        await this.runManager.addRun(run);
        const [result] = await run.getJobResults();
        return {result};
    }

    /**
     * Finish the Microsoft Sales configuration by providing the apiSetupData and accountIdentifier.
     * @param apiSetupData
     */

    @withSentryErrorHandling()
    public async configureMicrosoftSales(apiSetupData: MicrosoftApiSetupData[]): Promise<SourceAccount> {
        const config = await this.scraperConfigurationManager.getScraperConfigurationBySource(Source.MICROSOFT_SALES);
        if (!config) {
            telemetry.logAndThrowError(`${scraperNotFoundText} ${Source.MICROSOFT_SALES}`, {source: Source.MICROSOFT_SALES});
        }
        const sourceAccount = await this.scraperConfigurationManager.getSourceAccount(config.sourceAccountId);
        if (!sourceAccount) {
            telemetry.logAndThrowError(`Source account for ${Source.MICROSOFT_SALES} with id: ${config.sourceAccountId} not found.`, {source: Source.MICROSOFT_SALES});
        }

        return this.scraperConfigurationManager.editSourceAccount({
            id: config.sourceAccountId,
            cliParams: {apiSetupData},
            accountIdentifier: sourceAccount.accountIdentifier
        });
    }

    @withSentryErrorHandling()
    public async deleteScraperConfiguration(source: Source): Promise<void> {
        telemetry.trace(`Deleting scraper configuration for ${source}`);
        return this.scraperConfigurationManager.deleteScraperConfiguration(source);
    }

    /**
     * @deprecated
     */

    @withSentryErrorHandling()
    public async listSourceAccounts(_studioId: number): Promise<SourceAccount[]> {
        telemetry.trace(`Listing source accounts`);
        return this.scraperConfigurationManager.listSourceAccounts();
    }

    @withSentryErrorHandling()
    public async addSourceAccount(sourceAccount: SourceAccountAddParams): Promise<SourceAccount> {
        return this.scraperConfigurationManager.addSourceAccount(sourceAccount);
    }

    @withSentryErrorHandling()
    public async deleteSourceAccount(sourceAccountId: string): Promise<void> {
        telemetry.trace(`Deleting source account ${sourceAccountId}`);
        const sourceAccount = await this.scraperConfigurationManager.getSourceAccount(sourceAccountId);
        if (sourceAccount) {
            telemetry.trace(`Deleting source account session file ${sourceAccount.sessionPath}`);
            await removeFileOrDirectory(sourceAccount.sessionPath);
        }
        telemetry.trace(`Deleting source account ${sourceAccountId}`);
        return this.scraperConfigurationManager.deleteSourceAccount(sourceAccountId);
    }

    public setFeatureFlags(features: FeatureFlag[]): void {
        setFeatureFlags(features);
    }

    // noinspection JSUnusedGlobalSymbols
    public async setDebugModePath(path: string | undefined): Promise<void> {
        telemetry.trace(`Setting debug mode path`);
        await this.storage.saveScreenshotAndHtmlDumpDirPath(path);
    }

    // noinspection JSUnusedGlobalSymbols
    public async getDebugModePath(): Promise<string | undefined> {
        telemetry.trace(`Getting debug mode path`);
        return this.storage.getScreenshotAndHtmlDumpDirPath();
    }

    // noinspection JSUnusedGlobalSymbols
    public async getNewSources(): Promise<Source[]> {
        telemetry.trace(`Fetching new sources`);
        return getNewSourcesFromAPI();
    }

    public getRelatedSources(source: Source): Source[] {
        telemetry.trace(`Getting related sources`);
        return getRelatedSources(source);
    }

    private async runStorageMigrations(): Promise<void> {
        //Should be replaced this with a proper migration system, good enough solution for now
        telemetry.trace(`Running store migrations`);
        try {
            await runMigration(this.storage);
        } catch (error) {
            Emitter.emit({type: eventTypes.libError, message: STORAGE_MIGRATIONS_ERROR_MSG});
            telemetry.exception(error, false, {operation: 'runStorageMigrations'});
            throw error;
        }
    }

    private async startDailyScrapeCron(): Promise<void> {
        telemetry.trace(`Starting daily scrape cron`);
        return startDailyCron(
            await this.getDailyCronSchedule(),
            () => this.scrapeAllSources(true, TriggeredBy.SCHEDULE),
            () => this.stopAllScrapes()
        );
    }

    private async wasTodayScheduledRunMissed(): Promise<boolean> {
        return isNextScheduleTomorrow();
    }
}
