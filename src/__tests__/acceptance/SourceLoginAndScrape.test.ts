import {ScraperLib} from '../../ScraperLib';
import {Source} from '../../types/Source';
import {createDateRange} from '../utils/datesHelpers';
import {getCredentials, getFreshLib} from '../utils/helpers';
import {mockApiCoverage} from '../utils/scraperApiMocks';

const timeout = 20 * 60 * 1000; // because of time needed to download binaries

const sources = [
    // Source.MICROSOFT_SALES,
    // Source.HUMBLE_SALES,
    // - Source.GOG_SALES // captcha
    // Source.NINTENDO_SALES,
    // - Source.EPIC_SALES // Error
    // Source.STEAM_SALES,
    // Source.STEAM_WISHLISTS,
    // Source.STEAM_IMPRESSIONS,
    // Source.META_RIFT_SALES,
    // Source.META_QUEST_SALES,
    Source.PLAYSTATION_SALES
];

describe('login & scrape test', () => {
    let scraperLib: ScraperLib;

    beforeEach(async () => {
        scraperLib = await getFreshLib({shouldLogin: true});
    });
    afterEach(async () => {
        await scraperLib.close();
        jest.resetAllMocks();
    });

    test.each(sources)(
        `loginWithCredentials & scrape for %s`,
        async (source: Source) => {
            // We shouldn't use mocks for acceptance tests, but running it with real API communication can cause non-deterministic errors
            // Maybe we should rewrite those tests?
            mockApiCoverage([createDateRange()]);

            const credentials = getCredentials(source);

            await scraperLib.loginWithCredentials(source, credentials);

            const config = await scraperLib.getScraperConfiguration(source);
            expect(config).toBeDefined();
            const sourceAccount = await scraperLib.getSourceAccount(config!.sourceAccountId!);
            expect(sourceAccount?.sessionPath).toBeDefined();
            expect(sourceAccount?.sessionPath).toEqual(expect.stringContaining(source));

            const previousJob = await scraperLib.scrapeSource(config!.source);
            await previousJob!.jobFinishedPromise;
            const job = await scraperLib.scrapeSource(config!.source);
            await job?.jobFinishedPromise;

            const updatedConfig = await scraperLib.getScraperConfiguration(source);
            expect(updatedConfig?.lastSuccessfulScrapeDate).toBeDefined();
            expect(Math.abs(updatedConfig!.lastSuccessfulScrapeDate!.getTime() - new Date().getTime())).toBeLessThanOrEqual(3000);
        },
        timeout
    );
});
