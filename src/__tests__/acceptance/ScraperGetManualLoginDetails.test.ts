import {transformAndValidate} from 'class-transformer-validator';
import {ManualLoginDetailsResult} from '../../processes/types';
import {ScraperLib} from '../../ScraperLib';
import {Source} from '../../types/Source';
import {getFreshLib} from '../utils/helpers';

const timeout = 10 * 60 * 1000; // because of time needed to download binaries
describe('Source get manual login details', () => {
    let scraperLib: ScraperLib;

    beforeEach(async () => {
        scraperLib = await getFreshLib({shouldLogin: true});
    });

    afterEach(async () => {
        await scraperLib.close();
    });

    const TestableSources = [
        Source.GOG_SALES,
        Source.STEAM_SALES,
        Source.STEAM_IMPRESSIONS,
        Source.STEAM_WISHLISTS,
        Source.META_QUEST_SALES,
        Source.META_RIFT_SALES,
        Source.NINTENDO_SALES,
        Source.EPIC_SALES,
        Source.HUMBLE_SALES,
        Source.MICROSOFT_SALES
    ];
    test.each(TestableSources)(
        `Should return proper manual login details for %s`,
        async (source) => {
            const exec = await scraperLib.getManualLoginData(source);
            await transformAndValidate(ManualLoginDetailsResult, exec.result);
        },
        timeout
    );

    test(
        `Should return null for ${Source.PLAYSTATION_SALES}`,
        async () => {
            const exec = await scraperLib.getManualLoginData(Source.PLAYSTATION_SALES);
            expect(exec.result).toBeNull();
        },
        timeout
    );
});
