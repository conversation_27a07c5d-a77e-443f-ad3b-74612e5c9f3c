import {promises as fs} from 'fs';
import * as path from 'path';
import * as reportUpload from '../../api/reportUpload';
import * as ScraperA<PERSON> from '../../api/ScraperApi';
import {BinaryProxy} from '../../dependencies/BinaryProxy';
import {getChromiumId, getScraperId} from '../../dependencies/identifiers';
import {AwaitableJob} from '../../publicInterfaces/AwaitableJob';
import {ScrapeJob} from '../../runs/jobs/ScrapeJob';
import {UploadReportJob} from '../../runs/jobs/UploadReportJob';
import {ScraperLib} from '../../ScraperLib';
import {Command, ShadowModeTask, Source} from '../../types';
import {Deferred} from '../../utils/Deferred';
import {shadowRunSessionFileNamePart} from '../../utils/session';
import {createDateRange} from '../utils/datesHelpers';
import {getCredentials, getFreshLib, scraperLibMainDirectory} from '../utils/helpers';
import {mockFeatureFlags} from '../utils/scraperRunMocks';

// TODO this will download binary and it must exist, we could change it to mock it somehow
const timeout = 20 * 60 * 1000; // because of time needed to download binaries
const testSource = Source.PLAYSTATION_SALES;
const shadowModeTask: ShadowModeTask = {
    source: testSource,
    dateRanges: [createDateRange('2023-01-01', '2023-01-08')],
    command: Command.SCRAPE,
    scrapers: '0.110.10',
    chromium: 970485,
    id: '75b1b8d2-7e6f-47fa-b57b-cd90b059e793',
    label: 'label'
};

let getShadowModeTaskDeferred: Deferred<ShadowModeTask>;
let runShadowModeTaskDeferred: Deferred<void>;
let scraperLib: ScraperLib;

const runShadowModeTaskMockfn = async (task: ShadowModeTask) => {
    const awaitableJob: AwaitableJob = await ScraperLib.prototype['runShadowModeTask'].apply(scraperLib, [task]);
    await awaitableJob.jobFinishedPromise;

    runShadowModeTaskDeferred.resolve(); // this is to know when in fact shadow run finished
};

describe('shadow mode', () => {
    beforeEach(async () => {
        scraperLib = await getFreshLib({shouldLogin: true, skipInit: true});

        getShadowModeTaskDeferred = new Deferred<ShadowModeTask>();
        runShadowModeTaskDeferred = new Deferred<void>();

        jest.spyOn(ScraperApi, 'getShadowModeTask')
            .mockResolvedValue(undefined) // default value
            .mockResolvedValueOnce(getShadowModeTaskDeferred.promise); // first call

        //https://stackoverflow.com/questions/62171602/testing-private-method-using-spyon-and-jest
        jest.spyOn(scraperLib as any, 'runShadowModeTask').mockImplementationOnce(runShadowModeTaskMockfn);
        await addShadowDependencyToRealDependenciesInfo();
    }, timeout);

    afterEach(async () => {
        await scraperLib.close();
        jest.resetAllMocks();
    });

    test(
        'a ScrapeRun in shadow mode executes a ScrapeJob and UploadReportJob but does not send report upload info',
        async () => {
            const {binaryProxyRunSpy, scrapeJobExecuteSpy, uploadReportJobExecuteSpy, sendReportUploadInfoSpy} = await prepareMocksForTest(testSource, shadowModeTask);

            await runShadowModeTaskDeferred.promise;

            expect(scrapeJobExecuteSpy).toBeCalledTimes(1);
            expect(uploadReportJobExecuteSpy).toBeCalledTimes(1);
            expect(sendReportUploadInfoSpy).toBeCalledTimes(0);

            expect(binaryProxyRunSpy.mock.calls[0][0]).toEqual(Command.CHECK_SESSION);
            expect(binaryProxyRunSpy.mock.calls[1][0]).toEqual(Command.SCRAPE);
            expect(binaryProxyRunSpy.mock.calls[1][1].forcedScrapersExecPath).toContain(getScraperId(shadowModeTask.scrapers));
            expect(binaryProxyRunSpy.mock.calls[1][1].forcedChromiumExecPath).toContain(getChromiumId(shadowModeTask.chromium));

            const sessionFiles = fs.readdir(path.join(scraperLibMainDirectory, 'sessions'));
            for (const sessionFilePath in sessionFiles) {
                expect(sessionFilePath).not.toMatch(shadowRunSessionFileNamePart);
            }
        },
        timeout
    );
});

async function prepareMocksForTest(source: Source, taskToRun: ShadowModeTask) {
    await scraperLib.init({shadowMode: {active: true, interval: 30 * 1000}});
    const credentials = getCredentials(source);
    await scraperLib.loginWithCredentials(source, credentials);

    mockFeatureFlags(['shadow-mode-enabled']);

    const binaryProxyRunSpy = jest.spyOn(BinaryProxy.prototype, 'run');
    const scrapeJobExecuteSpy = jest.spyOn(ScrapeJob.prototype, 'execute');
    const uploadReportJobExecuteSpy = jest.spyOn(UploadReportJob.prototype, 'execute');
    const sendReportUploadInfoSpy = jest.spyOn(reportUpload, 'sendReportUploadInfo');

    getShadowModeTaskDeferred.resolve(taskToRun); // this will de facto unlock shadow run

    return {binaryProxyRunSpy, scrapeJobExecuteSpy, uploadReportJobExecuteSpy, sendReportUploadInfoSpy};
}

async function addShadowDependencyToRealDependenciesInfo() {
    const {default: defaultDependency, ...rest} = await ScraperApi.getDependenciesInfo();
    jest.spyOn(ScraperApi, 'getDependenciesInfo').mockResolvedValue({
        default: defaultDependency,
        ...rest,
        shadow_mode: ScraperApi.createScraperDependencyObject(shadowModeTask.scrapers, shadowModeTask.chromium, true)
    });
}
