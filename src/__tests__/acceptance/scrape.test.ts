import {ScraperConfigurationStatus} from '../../configurations/ScraperConfiguration';
import {ScraperLib} from '../../ScraperLib';
import {Source} from '../../types/Source';
import {getCredentials, getFreshLib} from '../utils/helpers';

const testSource = Source.PLAYSTATION_SALES;
const testScraperConfig = {source: testSource};

describe('Scraping with run manager', () => {
    let scraperLib: ScraperLib;
    beforeEach(async () => {
        scraperLib = await getFreshLib({shouldLogin: true});
    });

    afterEach(async () => {
        await scraperLib.close();
    });

    test('Jobs running on run manager are visible as running when listing all scraper configurations ', async () => {
        const credentials = getCredentials(testSource);
        await scraperLib.loginWithCredentials(testSource, credentials);

        await scraperLib.scrapeSource(testSource, true);

        const configurations = await scraperLib.listScraperConfigurations();

        expect(configurations).toEqual([
            expect.objectContaining({
                source: testScraperConfig.source,
                status: ScraperConfigurationStatus.RUNNING_SCRAPE
            })
        ]);
    }, 100000);

    test('User is able to stop a job in the run manager', async () => {
        const credentials = getCredentials(testSource);
        await scraperLib.loginWithCredentials(testSource, credentials);

        await scraperLib.scrapeSource(testSource, true);
        await scraperLib.stopScrape(testSource);

        const configurations = await scraperLib.listScraperConfigurations();

        expect(configurations).toEqual([
            expect.objectContaining({
                source: testScraperConfig.source,
                status: ScraperConfigurationStatus.CONFIGURED
            })
        ]);
    }, 100000);

    test('User is able to stop login run', async () => {
        const credentials = getCredentials(testSource);

        const jobPromise = scraperLib.loginWithCredentials(testSource, credentials);

        // give the queue some time to start the job
        await new Promise((resolve) => setTimeout(resolve, 3000));

        await scraperLib.stopLogin(testSource);

        await expect(jobPromise).rejects.toThrow('Operation terminated');

        const configurations = await scraperLib.listScraperConfigurations();

        expect(configurations).toEqual([]);
    }, 100000);

    test('User is able to stop a job in the run manager using kill all', async () => {
        const credentials = getCredentials(testSource);
        await scraperLib.loginWithCredentials(testSource, credentials);

        await scraperLib.scrapeSource(testSource, true);
        await scraperLib.stopAllScrapes();

        const configurations = await scraperLib.listScraperConfigurations();

        expect(configurations).toEqual([
            expect.objectContaining({
                source: testScraperConfig.source,
                status: ScraperConfigurationStatus.CONFIGURED
            })
        ]);
    }, 100000);

    // Skipped test, because currently we do not have related sources, which can be tested without 2FA, which is not supported in CI
    test.skip('User should be able to scrape related-source using temporary configuration based on other source', async () => {
        const testSource = Source.NINTENDO_SALES;
        // make sure nintendo discounts is disabled to prevent config creation
        scraperLib.setFeatureFlags([]);

        // login to nintendo_sales source which should NOT create config for nintendo_discounts
        const credentials = getCredentials(testSource);
        await scraperLib.loginWithCredentials(testSource, credentials);

        const configs = await scraperLib.listScraperConfigurations();

        expect(configs.some((config) => config.source === Source.NINTENDO_DISCOUNTS)).toBe(false);

        // enable nintendo discounts
        scraperLib.setFeatureFlags(['nintendo_discounts_scrapers']);

        // scrape nintendo discounts which should use temporary configuration based on nintendo_sales
        await scraperLib.scrapeSource(Source.NINTENDO_DISCOUNTS, true);
    }, 600000);
});
