import {errorType} from '../../configurations/errorType';
import {ScraperConfigurationAddParams, ScraperConfigurationEditParams, ScraperConfigurationStatus} from '../../configurations/ScraperConfiguration';
import {SourceAccountAddParams} from '../../configurations/SourceAccount';
import {ScraperLib} from '../../ScraperLib';
import {Source} from '../../types/Source';
import {getFreshLib, testSourceAccountWithParams} from '../utils/helpers';

const testSourceAccount: SourceAccountAddParams = testSourceAccountWithParams();
const testScraperConfig = {source: Source.EPIC_SALES};

describe('Scraper configuration', () => {
    let scraperLib: ScraperLib;

    beforeEach(async () => {
        scraperLib = await getFreshLib();
    });

    afterEach(async () => {
        await scraperLib.close();
    });

    describe('For each IndieBI organization I have access to, I can ', () => {
        test('add a ScraperConfig', async () => {
            const result = await scraperLib.addScraperConfiguration(testScraperConfig, testSourceAccount);
            expect(result).toEqual(expect.objectContaining(testScraperConfig));
        });

        test('edit a ScraperConfig', async () => {
            const newConfig = await scraperLib.addScraperConfiguration(testScraperConfig, testSourceAccount);
            const modifiedScraperConfig = {...newConfig, status: ScraperConfigurationStatus.ERROR};
            const result2 = await scraperLib.editScraperConfiguration(modifiedScraperConfig);
            expect(modifiedScraperConfig.status).toBe(result2.status);
        });
    });

    describe.skip(
        'When adding or editing scraper configuration, I can specify' +
            'which source I want to scrape from' +
            'which source account I want to use' +
            'which source-side organizations I want to scrape from (possibly multiple)' +
            'which SKUs to filter out (if possible in the source).',
        () => {
            test('Adding case:', async () => {
                const newScraperConfig: ScraperConfigurationAddParams = {
                    source: Source.GOG_SALES,
                    status: ScraperConfigurationStatus.CONFIGURED,
                    sourceAccountId: '-1',
                    sourceSideOrganizationsToScrape: ['sourceSideOrgOne', 'sourceSideOrgTwo'],
                    skuToIgnore: ['ignore_1', 'ignore_2']
                };
                const result = await scraperLib.addScraperConfiguration(newScraperConfig, testSourceAccount);
                expect(result).toEqual(expect.objectContaining(newScraperConfig));
                expect(result.id).toBeDefined();
            });

            test('Edit case:', async () => {
                const configuration = await scraperLib.addScraperConfiguration(testScraperConfig, testSourceAccount);

                const newScraperConfig: ScraperConfigurationEditParams = {
                    id: configuration.id,
                    source: Source.GOG_SALES,
                    status: ScraperConfigurationStatus.CONFIGURED,
                    sourceAccountId: '-1',
                    sourceSideOrganizationsToScrape: ['sourceSideOrgOne', 'sourceSideOrgTwo'],
                    skuToIgnore: ['ignore_1', 'ignore_2']
                };

                const result = await scraperLib.editScraperConfiguration(newScraperConfig);
                expect(result).toEqual(expect.objectContaining(newScraperConfig));
            });
        }
    );

    test('I can view all the scraper configurations I have created.', async () => {
        const scraperConfigTwo = {id: '-2', source: Source.GOG_SALES};

        await scraperLib.addScraperConfiguration(testScraperConfig, testSourceAccount);
        await scraperLib.addScraperConfiguration(scraperConfigTwo, testSourceAccount);

        const configurations = await scraperLib.listScraperConfigurations();
        expect(configurations).toHaveLength(2);
        expect(configurations.map((config) => config.source)).toEqual(expect.arrayContaining([Source.EPIC_SALES, Source.GOG_SALES]));
    });

    test("I can delete a scraper configuration I don't need ", async () => {
        const {source} = await scraperLib.addScraperConfiguration(testScraperConfig, testSourceAccount);
        await scraperLib.deleteScraperConfiguration(source);
        expect(await scraperLib.listScraperConfigurations()).toEqual([]);
    });

    describe('If a scraping configuration is configured with a source account that has an expired session: ', () => {
        test('I see that on the list.', async () => {
            const baseScraperConfig = {
                source: Source.EPIC_SALES
            };

            const addResults = await scraperLib.addScraperConfiguration(baseScraperConfig, testSourceAccount);
            await scraperLib.editScraperConfiguration({
                ...addResults,
                status: ScraperConfigurationStatus.ERROR,
                errorType: errorType.SESSION_EXPIRED
            });

            const configurations = await scraperLib.listScraperConfigurations();
            expect(configurations.length).toBe(1);
            expect(configurations[0]).toEqual(
                expect.objectContaining({
                    ...baseScraperConfig,
                    status: ScraperConfigurationStatus.ERROR,
                    errorType: errorType.SESSION_EXPIRED
                })
            );
        });

        //TODO fix this test!
        // test('I have the option to refresh the session.', async () => {
        //     const {scraperLib} = await getFreshLibWithOrganization();
        //     const expiredScraperConfig = {
        //         source: Source.EPIC_SALES,
        //         status: ScraperConfigStatus.SESSION_EXPIRED,
        //         sourceAccount: {id: '', session: 'oldSession'}
        //     };

        //     await scraperLib.addScraperConfiguration(expiredScraperConfig);

        //     const newSession = 'newSession';
        //     await scraperLib.editSourceAccount({id: expiredScraperConfig.sourceAccount!.id, session: newSession});

        //     const configurations = await scraperLib.listScraperConfigurations();
        //     const sourceAccount = scraperLib.getSourceAccount(configurations[0].sourceAccountId!);
        //     expect(sourceAccount.sessionPath).toEqual(newSession);
        // });
    });
});
