import {ScraperLib} from '../../ScraperLib';
import {Source} from '../../types/Source';
import {getCredentials, getFreshLib} from '../utils/helpers';

const timeout = 20 * 60 * 1000; // because of time needed to download binaries

const testableSources = [Source.PLAYSTATION_SALES];

/**
 * Currently this method is only used in electron app for Microsoft organization setup.
 * I added Playstation as a test case for development since it's quicker to run.
 */
describe('getSourceSideOrganizations', () => {
    let scraperLib: ScraperLib;

    beforeEach(async () => {
        scraperLib = await getFreshLib({shouldLogin: true});
    });

    afterEach(async () => {
        await scraperLib.close();
    });

    test.each(testableSources)(
        `getSourceSideOrganizations for %s`,
        async (source: Source) => {
            const credentials = getCredentials(source);
            await scraperLib.loginWithCredentials(source, credentials);
            const organizations = await scraperLib.getSourceSideOrganizations(source);

            expect(organizations.result).toBeInstanceOf(Array);

            organizations.result.forEach((organization) => {
                expect(organization).toMatchObject({
                    id: expect.any(String),
                    name: expect.any(String)
                });

                if ('hasScrapeBlockingIssues' in organization) {
                    expect(organization.hasScrapeBlockingIssues).toEqual(expect.any(Boolean));
                }
            });
        },
        timeout
    );
});
