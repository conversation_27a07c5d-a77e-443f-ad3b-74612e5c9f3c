import {ScraperConfiguration, ScraperConfigurationStatus} from '../../../configurations/ScraperConfiguration';
import {ScraperConfigurationManager} from '../../../configurations/ScraperConfigurationManager';
import {ElectronStorageImplementation} from '../../../storage/ElectronStorageImplementation';
import {iScraperServiceClient} from '../../../telemetry/scraperService/iScraperServiceClient';
import * as telemetry from '../../../telemetry/telemetry';
import {Source} from '../../../types';
import * as fileUtils from '../../../utils/fileUtils';
import {newAdapterWithStorage} from '../../utils/helpers';

const createMockScraperServiceClient = (): jest.Mocked<iScraperServiceClient> =>
    ({
        scheduleScraperStateChangedEvent: jest.fn(),
        scheduleLoginStateChangedEvent: jest.fn()
    } as unknown as jest.Mocked<iScraperServiceClient>);

const createMockScraperConfiguration = (overrides: Partial<ScraperConfiguration> = {}): ScraperConfiguration => ({
    id: 'config-1',
    source: Source.STEAM_SALES,
    sourceAccountId: 'account-1',
    status: ScraperConfigurationStatus.CONFIGURED,
    createdAt: new Date(),
    ...overrides
});

const getScraperConfigurationManagerMocks = () => {
    const {adapter, memory} = newAdapterWithStorage();
    const storage = new ElectronStorageImplementation(adapter);
    const mockClient = createMockScraperServiceClient();
    const scraperConfigurationManager = new ScraperConfigurationManager(storage, mockClient);

    jest.spyOn(telemetry, 'trace').mockImplementation();

    return {storage, mockClient, scraperConfigurationManager, memory};
};

describe('ScraperConfigurationManager', () => {
    let scraperConfigurationManager: ScraperConfigurationManager;

    beforeEach(() => {
        jest.restoreAllMocks();
        ({scraperConfigurationManager} = getScraperConfigurationManagerMocks());
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should return scraper configuration', async () => {
        await scraperConfigurationManager.addScraperConfiguration(createMockScraperConfiguration({source: Source.GOG_SALES}));
        await scraperConfigurationManager.addScraperConfiguration(createMockScraperConfiguration({source: Source.EPIC_SALES}));

        const result = await scraperConfigurationManager.getScraperConfigurations();

        expect(result.map((entry) => entry.source).sort()).toEqual([Source.EPIC_SALES, Source.GOG_SALES].sort());
    });

    it('should return the scraper configuration for the specified source', async () => {
        await scraperConfigurationManager.addScraperConfiguration(createMockScraperConfiguration({source: Source.GOG_SALES}));
        await scraperConfigurationManager.addScraperConfiguration(createMockScraperConfiguration({source: Source.EPIC_SALES}));

        const result = await scraperConfigurationManager.getScraperConfiguration(Source.EPIC_SALES);

        expect(result).toHaveProperty('source', Source.EPIC_SALES);
    });

    it('should return undefined when getting non-existent scraper configuration', async () => {
        const result = await scraperConfigurationManager.getScraperConfiguration(Source.STEAM_SALES);

        expect(result).toBeUndefined();
    });

    it('should create new configurations for all related sources when none exist', async () => {
        await scraperConfigurationManager.addScraperConfiguration(createMockScraperConfiguration({source: Source.STEAM_WISHLISTS}));

        const result = await scraperConfigurationManager.getScraperConfigurations();

        expect(result.map((entry) => entry.source).sort()).toEqual([Source.STEAM_WISHLISTS, Source.STEAM_SALES].sort());
    });

    it('should update existing configurations with new source account when configurations already exist', async () => {
        const oldConfigs = await scraperConfigurationManager.addScraperConfiguration(createMockScraperConfiguration({source: Source.STEAM_WISHLISTS}));
        const oldAccountId = oldConfigs.sourceAccountId;
        await scraperConfigurationManager.deleteScraperConfiguration(Source.STEAM_WISHLISTS);

        const newConfigs = await scraperConfigurationManager.addScraperConfiguration(
            createMockScraperConfiguration({source: Source.STEAM_WISHLISTS, sourceAccountId: 'fizBazFoo'})
        );
        const newAccountId = newConfigs.sourceAccountId;

        const result = await scraperConfigurationManager.getScraperConfigurations();

        expect(result.map((entry) => entry.source).sort()).toEqual([Source.STEAM_WISHLISTS, Source.STEAM_SALES].sort());
        expect(result[0].sourceAccountId).not.toEqual(oldAccountId);
        expect(result[1].sourceAccountId).not.toEqual(oldAccountId);
        expect(result[0].sourceAccountId).toEqual(newAccountId);
        expect(result[1].sourceAccountId).toEqual(newAccountId);
    });

    it('should remove source accounts that are not referenced by any configuration', async () => {
        const {scraperConfigurationManager, storage} = getScraperConfigurationManagerMocks();
        await scraperConfigurationManager.addSourceAccount({accountIdentifier: '<EMAIL>', sessionPath: '/path/to/session.json'});

        await scraperConfigurationManager.removeAllUnusedSourceAccounts();

        expect(await storage.listSourceAccounts()).toEqual([]);
    });

    it('should handle deletion gracefully when source account does not exist', async () => {
        await expect(scraperConfigurationManager.deleteSourceAccount('nonexistent-account')).resolves.not.toThrow();
    });

    it('should disable scraper configuration and send state changed event', async () => {
        const {scraperConfigurationManager, mockClient} = getScraperConfigurationManagerMocks();
        await scraperConfigurationManager.addScraperConfiguration(createMockScraperConfiguration({source: Source.STEAM_SALES}));

        const result = await scraperConfigurationManager.disableScraperConfiguration(Source.STEAM_SALES);

        expect(result.status).toBe(ScraperConfigurationStatus.DISABLED);
        expect(result.source).toBe(Source.STEAM_SALES);
        expect(mockClient.scheduleScraperStateChangedEvent).toHaveBeenCalledWith({
            source: Source.STEAM_SALES,
            triggeredBy: 'USER_VIA_ELECTRON',
            newState: 'DISABLED',
            operationId: expect.any(String)
        });
    });

    it('should enable scraper configuration with CONFIGURED status when no error', async () => {
        const {scraperConfigurationManager, mockClient} = getScraperConfigurationManagerMocks();
        await scraperConfigurationManager.addScraperConfiguration(
            createMockScraperConfiguration({
                source: Source.STEAM_SALES
            })
        );
        await scraperConfigurationManager.disableScraperConfiguration(Source.STEAM_SALES);

        const result = await scraperConfigurationManager.enableScraperConfiguration(Source.STEAM_SALES);

        expect(result.status).toBe(ScraperConfigurationStatus.CONFIGURED);
        expect(result.source).toBe(Source.STEAM_SALES);
        expect(mockClient.scheduleScraperStateChangedEvent).toHaveBeenCalledWith({
            source: Source.STEAM_SALES,
            triggeredBy: 'USER_VIA_ELECTRON',
            newState: 'FINISHED',
            operationId: expect.any(String)
        });
    });

    it('should throw error when enabling non-existent scraper configuration', async () => {
        await expect(scraperConfigurationManager.enableScraperConfiguration(Source.STEAM_SALES)).rejects.toThrow('Scraper configuration not found for steam_sales');
    });

    it('adding scraper configuration should return the newly added configuration', async () => {
        const {scraperConfigurationManager} = getScraperConfigurationManagerMocks();
        const configParams = createMockScraperConfiguration({source: Source.GOG_SALES});

        const result = await scraperConfigurationManager.addScraperConfiguration(configParams);

        expect(result.source).toBe(Source.GOG_SALES);
        expect(result.id).not.toEqual(configParams.id);
        expect(result.sourceAccountId).toBe('account-1');
        expect(result.status).toBe(ScraperConfigurationStatus.CONFIGURED);
    });

    it('should add scraper configuration with source account', async () => {
        const configParams = createMockScraperConfiguration({source: Source.GOG_SALES});
        const sourceAccountParams = {accountIdentifier: '<EMAIL>', sessionPath: '/path.json'};

        const result = await scraperConfigurationManager.addScraperConfiguration(configParams, sourceAccountParams);

        expect(result.source).toBe(Source.GOG_SALES);
        expect(result.sourceAccountId).toBe('account-1');
        expect(result.status).toBe(ScraperConfigurationStatus.CONFIGURED);
    });

    it('should edit scraper configuration', async () => {
        await scraperConfigurationManager.addScraperConfiguration(createMockScraperConfiguration({source: Source.STEAM_SALES}));

        const result = await scraperConfigurationManager.editScraperConfiguration({
            source: Source.STEAM_SALES,
            status: ScraperConfigurationStatus.DISABLED
        });

        expect(result.status).toBe(ScraperConfigurationStatus.DISABLED);
        expect(result.source).toBe(Source.STEAM_SALES);
    });

    it('should add source account', async () => {
        const accountParams = {accountIdentifier: '<EMAIL>', sessionPath: '/new/path.json'};

        const result = await scraperConfigurationManager.addSourceAccount(accountParams);

        expect(result.accountIdentifier).toBe('<EMAIL>');
        expect(result.sessionPath).toBe('/new/path.json');
        expect(result.id).toBeDefined();
    });

    it('should get source account', async () => {
        const accountParams = {accountIdentifier: '<EMAIL>', sessionPath: '/path.json'};
        const addedAccount = await scraperConfigurationManager.addSourceAccount(accountParams);

        const result = await scraperConfigurationManager.getSourceAccount(addedAccount.id);

        expect(result).toEqual(addedAccount);
        expect(result?.accountIdentifier).toBe('<EMAIL>');
    });

    it('should return undefined when getting non-existent source account', async () => {
        const result = await scraperConfigurationManager.getSourceAccount('non-existent-id');

        expect(result).toBeUndefined();
    });

    it('should get scraper configuration by source with optional parameters', async () => {
        await scraperConfigurationManager.addScraperConfiguration(createMockScraperConfiguration({source: Source.EPIC_SALES}));

        const result = await scraperConfigurationManager.getScraperConfigurationBySource(Source.EPIC_SALES, true, false);

        expect(result?.source).toBe(Source.EPIC_SALES);
        expect(result?.status).toBe(ScraperConfigurationStatus.CONFIGURED);
    });

    it('should return undefined when getting non-existent configuration by source', async () => {
        const result = await scraperConfigurationManager.getScraperConfigurationBySource(Source.STEAM_SALES, false, false);

        expect(result).toBeUndefined();
    });

    it('should edit source account', async () => {
        const addedAccount = await scraperConfigurationManager.addSourceAccount({
            accountIdentifier: '<EMAIL>',
            sessionPath: '/original/path.json'
        });

        const result = await scraperConfigurationManager.editSourceAccount({
            id: addedAccount.id,
            accountIdentifier: '<EMAIL>',
            sessionPath: '/updated/path.json'
        });

        expect(result.accountIdentifier).toBe('<EMAIL>');
        expect(result.sessionPath).toBe('/updated/path.json');
        expect(result.id).toBe(addedAccount.id);
    });

    it('should fail to update account identifier', async () => {
        const addedAccount = await scraperConfigurationManager.addSourceAccount({
            accountIdentifier: '<EMAIL>',
            sessionPath: '/original/path.json'
        });

        await expect(
            scraperConfigurationManager.editSourceAccount({
                id: addedAccount.id,
                accountIdentifier: '<EMAIL>',
                sessionPath: '/updated/path.json'
            })
        ).rejects.toThrow('Changing user identifier is not permitted');
    });

    it('should delete scraper configuration', async () => {
        await scraperConfigurationManager.addScraperConfiguration(createMockScraperConfiguration({source: Source.STEAM_SALES}));

        await scraperConfigurationManager.deleteScraperConfiguration(Source.STEAM_SALES);

        const result = await scraperConfigurationManager.getScraperConfiguration(Source.STEAM_SALES);
        expect(result).toBeUndefined();
    });

    it('should list source accounts', async () => {
        await scraperConfigurationManager.addSourceAccount({
            accountIdentifier: '<EMAIL>',
            sessionPath: '/path1.json'
        });
        await scraperConfigurationManager.addSourceAccount({
            accountIdentifier: '<EMAIL>',
            sessionPath: '/path2.json'
        });

        const result = await scraperConfigurationManager.listSourceAccounts();

        expect(result).toHaveLength(2);
        expect(result.map((acc) => acc.accountIdentifier).sort()).toEqual(['<EMAIL>', '<EMAIL>']);
    });

    it('should return empty array when no source accounts exist', async () => {
        const result = await scraperConfigurationManager.listSourceAccounts();

        expect(result).toEqual([]);
    });

    it('should handle file removal errors gracefully when deleting source account', async () => {
        const account = await scraperConfigurationManager.addSourceAccount({
            accountIdentifier: '<EMAIL>',
            sessionPath: '/problematic/path.json'
        });

        jest.spyOn(fileUtils, 'removeFileOrDirectory').mockRejectedValue(new Error('File removal failed'));

        await expect(scraperConfigurationManager.deleteSourceAccount(account.id)).rejects.toThrow('File removal failed');
    });
});
