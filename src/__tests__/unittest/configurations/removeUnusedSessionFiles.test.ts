import {promises as fs} from 'fs';
import * as path from 'path';
import * as mockFs from 'mock-fs';
import removeUnusedSessionFiles from '../../../configurations/removeUnusedSessionFiles';
import {ScraperConfigurationStatus} from '../../../configurations/ScraperConfiguration';
import {BinaryProxy} from '../../../dependencies/BinaryProxy';
import {Source} from '../../../types';
import {shadowRunSessionFileNamePart} from '../../../utils/session';
import {getFreshLib, processResultMock, scraperLibMainDirectory, testSourceAccountWithParams} from '../../utils/helpers';
import {mockDependencies, mockFeatureFlags} from '../../utils/scraperRunMocks';

const credentials = {user: 'user', password: 'password'};
const sessionsPath = path.join(scraperLibMainDirectory, 'sessions');
const binRunSpy = jest.spyOn(BinaryProxy.prototype, 'run');
const {accountIdentifier} = testSourceAccountWithParams();

describe('removeUnusedSessionFiles', () => {
    beforeEach(async () => {
        binRunSpy.mockImplementation(
            processResultMock({
                hasScrapeBlockingIssues: false,
                id: accountIdentifier
            })
        );
        mockFeatureFlags();
        mockDependencies();
    });
    afterEach(() => {
        mockFs.restore();
        jest.resetAllMocks();
    });

    test('should delete files not used by active source accounts', async () => {
        //given
        const scraperLib = await getFreshLib({withMockPriorityQueue: true});
        const asd = await scraperLib.loginWithCredentials(Source.STEAM_SALES, credentials);
        const sourceAccount = await scraperLib.getSourceAccount(asd[0].sourceAccountId);
        const splitPath = sourceAccount!.sessionPath.split('/');
        const realSessionFileName = splitPath[splitPath.length - 1];
        mockFs({
            [path.join(scraperLib['mainDir'], 'sessions')]: {
                'file1.json': '',
                'file2.json': '',
                'file3.json': '',
                [realSessionFileName]: '',
                [`name_${shadowRunSessionFileNamePart}_file.json`]: ''
            }
        });

        //when
        await removeUnusedSessionFiles(scraperLib['storage'], scraperLib['mainDir']);

        //then
        expect(await fs.readdir(sessionsPath)).toHaveLength(1);
    });

    test('should disable and enable source configuration', async () => {
        const scraperLib = await getFreshLib({withMockPriorityQueue: true});
        const config = await scraperLib.addScraperConfiguration({source: Source.GOG_SALES, sourceAccountId: '123'});
        expect(config.status).toBe(ScraperConfigurationStatus.CONFIGURED);

        const disabledConfig = await scraperLib.disableScraperConfiguration(Source.GOG_SALES);
        expect(disabledConfig.status).toBe(ScraperConfigurationStatus.DISABLED);

        const undisabledConfig = await scraperLib.enableScraperConfiguration(Source.GOG_SALES);
        expect(undisabledConfig.status).toBe(ScraperConfigurationStatus.CONFIGURED);
    });

    test('should disable and enable source configuration with error', async () => {
        const scraperLib = await getFreshLib({withMockPriorityQueue: true});
        await scraperLib.addScraperConfiguration({source: Source.GOG_SALES, sourceAccountId: '123', errorType: 'INCORRECT_CREDENTIALS'});

        const disabledConfig = await scraperLib.disableScraperConfiguration(Source.GOG_SALES);
        expect(disabledConfig.status).toBe(ScraperConfigurationStatus.DISABLED);

        const undisabledConfig = await scraperLib.enableScraperConfiguration(Source.GOG_SALES);
        expect(undisabledConfig.status).toBe(ScraperConfigurationStatus.ERROR);
    });
});
