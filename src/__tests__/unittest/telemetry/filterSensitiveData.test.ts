import * as util from 'util';
import {censorSensitiveMessage, filterSensitiveData, filterSensitiveFields, sensitiveFieldsArray} from '../../../telemetry/filterSensitiveData';

const EXPECTED_REDACTED_MESSAGE = '[REDACTED]';

describe('sensitivity filter should filter out sensitive params', () => {
    it('should filter out sensitive params', () => {
        const params = [
            '--source=source',
            '--output=json',
            '--apiUrl=apiUrl',
            '--apiToken=apiToken',
            '--credentials={"name":"name","password":"password"}',
            '--password=password',
            '--apiSetupData=apiSetupData',
            '--clientId=clientId',
            '--tenantId=tenantId',
            '--totpSecret=totpSecret',
            '--cloudStorageBucket=cloudStorageBucket',
            '--token=token'
        ];
        const filteredParams = filterSensitiveData(params);
        expect(filteredParams).toEqual(['--source=source', '--output=json', '--apiUrl=apiUrl']);
    });

    it('should filter out sensitive fields in arrays', () => {
        const input = {
            nested: [{}, {yes: true}, {a: 1}, {password: 'pass', safe: 0}]
        };
        const output = filterSensitiveFields(input);
        expect(output).toEqual({
            nested: [{}, {yes: true}, {a: 1}, {safe: 0}]
        });
    });

    it('should filter out sensitive fields', () => {
        const input = {
            name: 'name',
            password: 'password',
            apiSetupData: 'apiSetupData',
            clientId: 'clientId',
            tenantId: 'tenantId',
            totpSecret: 'totpSecret',
            cloudStorageBucket: 'cloudStorageBucket',
            token: 'token',
            safe: {
                butNotReally: {
                    password: 'password',
                    thisIsFine: true,
                    safe: 0
                }
            },
            headers: {
                Authorization: 'Bearer token'
            },
            spawnargs: ['--credentials={"user":"user","password":"password","totpSecret":""}'],
            test: ['--credentials={"user":"user","password":"password","totpSecret":""}', 'safe']
        };
        const output = filterSensitiveFields(input);
        expect(output).toEqual({
            name: 'name',
            safe: {
                butNotReally: {
                    thisIsFine: true,
                    safe: 0
                }
            },
            headers: {},
            test: ['[REDACTED]', 'safe']
        });
    });

    it('should filter sensitive fields regardless of case', () => {
        const input = {
            headers: {
                Authorization: 'Bearer token',
                authorization: 'Bearer token'
            }
        };
        const output = filterSensitiveFields(input);
        expect(output).toEqual({
            headers: {}
        });
    });

    it('should return empty object if input is empty', () => {
        const output = filterSensitiveFields();
        expect(output).toEqual({});
    });

    it('should properly handle circular structures', () => {
        const input = {
            a: {
                b: {
                    c: {}
                }
            }
        };
        input.a.b.c = input.a;
        const output = filterSensitiveFields(input);
        expect(output).toEqual({
            a: {
                b: {
                    c: '[Circular ~.a]'
                }
            }
        });
    });

    it('should handle arrays nested in objects', () => {
        const input = {
            a: 'x',
            password: 'password',
            params: ['--credentials={}', '--apiToken=token', '--option=option'],
            objects: [{password: 'password'}, {safe: 'safe'}]
        };
        const output = filterSensitiveFields(input);
        expect(output).toEqual({
            a: 'x',
            params: ['[REDACTED]', '[REDACTED]', '--option=option'],
            objects: [{}, {safe: 'safe'}]
        });
    });

    it.each(sensitiveFieldsArray)('should censor message with deliberate sensitive data %p', (sensitiveField) => {
        const message = `This is a message with a ${sensitiveField}: "${sensitiveField}" and a token`;
        const censoredMessage = censorSensitiveMessage(message);

        expect(censoredMessage).toEqual(EXPECTED_REDACTED_MESSAGE);
    });

    it.each(sensitiveFieldsArray)('should censor JSON.stringify format with object containing %p', (sensitiveField) => {
        const defaultJson = {[sensitiveField]: sensitiveField};
        const censoredMessage = censorSensitiveMessage(JSON.stringify(defaultJson));
        expect(censoredMessage).toEqual(EXPECTED_REDACTED_MESSAGE);
    });

    it.each(sensitiveFieldsArray)('should censor util.Inspect format with object containing %p', (sensitiveField) => {
        const censoredMessage = censorSensitiveMessage(util.inspect({[sensitiveField]: sensitiveField}));
        expect(censoredMessage).toEqual(EXPECTED_REDACTED_MESSAGE);
    });

    it('should not censor default toString since it does not leak sensitive data', () => {
        const censoredMessage = censorSensitiveMessage({password: 'password', token: 'fizBaz'}.toString());
        expect(censoredMessage).toEqual('[object Object]');
    });
    it.each(sensitiveFieldsArray)('should censor unix shell params containing %p', (sensitiveField) => {
        const censoredMessage = censorSensitiveMessage(`--${sensitiveField}=value`);
        expect(censoredMessage).toEqual(EXPECTED_REDACTED_MESSAGE);
    });
});
