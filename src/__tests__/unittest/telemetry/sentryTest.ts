import * as Sentry from '@sentry/node';
import {ScraperLib} from '../../../ScraperLib';
import {Source} from '../../../types';
import {getFreshLib} from '../../utils/helpers';

describe('Sentry integration should', () => {
    afterEach(async () => {
        jest.restoreAllMocks();
    });

    it('send error to sentry', async () => {
        const sentryCaptureExceptionSpy = jest.spyOn(Sentry, 'captureException');
        const scraperLib: ScraperLib = await getFreshLib({sentryDSN: 'https://<EMAIL>/FAKE', withMockPriorityQueue: true});

        try {
            await scraperLib.enableScraperConfiguration(Source.APP_STORE_SALES);
        } catch {
            // ignore the error
        }

        expect(sentryCaptureExceptionSpy).toHaveBeenCalled();
    });
});
