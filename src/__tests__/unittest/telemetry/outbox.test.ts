import {promises as fs} from 'fs';
import * as mockFs from 'mock-fs';
import {ScraperServiceLogLevel} from '../../../processes/types';
import {Outbox} from '../../../telemetry/scraperService/outbox';
import {NewScraperState} from '../../../telemetry/scraperService/scraperServiceEvents';
import {checkFileExists} from '../../../utils/fileUtils';

describe('ScraperService should:', () => {
    afterEach(() => {
        mockFs.restore();
        jest.restoreAllMocks();
    });

    const fakeMessage = {
        _id: 'fake',
        message: 'fake',
        level: ScraperServiceLogLevel.INFO,
        origin: 'fake',
        client_timestamp: new Date('2021-01-01T00:00:00.000Z'),
        operation_id: 'fake',
        machine_info: {
            arch: 'fake',
            homedir: 'fake',
            hostname: 'fake',
            platform: 'fake',
            release: 'fake',
            os_type: 'fake',
            os_version: 'fake',
            city: 'fakeCity',
            region: 'fakeRegion',
            country: 'fakeCountry'
        }
    };

    const fakeEvent = {
        event_type: 'scraper_state_changed',
        client_timestamp: new Date('2021-01-01T01:00:00.000Z'),
        origin: 'fake',
        operation_id: 'fake',
        body: {
            new_state: 'STARTED',
            triggered_by: 'USER_VIA_ELECTRON',
            source: 'steam_sales',
            account_identifier: 'fake',
            reason: 'test',
            machineInfo: fakeMessage.machine_info
        }
    };
    const defaultDumpFilePath = '.private/outbox_dump.json';

    it('should add messages to outbox', () => {
        //Given
        const outbox = new Outbox(defaultDumpFilePath);

        //When
        outbox.addMessages([fakeMessage]);

        //Then
        expect(outbox.getTraceBatchToSend()).toEqual([fakeMessage]);
    });

    it('should dump outbox to file', async () => {
        //Given
        mockFs({[defaultDumpFilePath]: ''});
        const outbox = new Outbox(defaultDumpFilePath);
        outbox.addMessages([fakeMessage]);

        //When
        await outbox.dumpOutboxToFile();

        //Then
        const fileExists = await checkFileExists(defaultDumpFilePath);
        expect(fileExists).toBe(true);
    });

    it('should read outbox from file', async () => {
        mockFs({[defaultDumpFilePath]: JSON.stringify(fakeMessage)});

        const outbox = new Outbox(defaultDumpFilePath);
        await outbox.loadOutboxFromFile();
        const batch = outbox.getTraceBatchToSend();

        expect(batch).toEqual([fakeMessage]);
    });

    it('should ignore broken outbox file and not throw', async () => {
        mockFs({[defaultDumpFilePath]: 'not a json'});

        const errorSpy = jest.spyOn(console, 'error').mockImplementation();

        const outbox = new Outbox(defaultDumpFilePath);
        await outbox.loadOutboxFromFile();
        const batch = outbox.getTraceBatchToSend();

        expect(batch).toEqual([]);
        expect(errorSpy).toHaveBeenCalledWith('Error parsing JSON item', expect.anything());
    });

    it('should read a corrupted and a valid outbox file and return only valid messages', async () => {
        //Given
        const invalidMessage = 'not a json';
        mockFs({[defaultDumpFilePath]: `${invalidMessage}\n${JSON.stringify(fakeMessage)}\n${JSON.stringify(fakeEvent)}`});
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();

        //When
        const outbox = new Outbox(defaultDumpFilePath);
        await outbox.loadOutboxFromFile();
        const batch = outbox.getTraceBatchToSend();
        const event = outbox.getEventToSend();

        //Then
        expect(batch).toEqual([fakeMessage]);
        expect(event).toEqual(fakeEvent);
        expect(errorSpy).toHaveBeenCalledWith('Error parsing JSON item', expect.anything());
    });
    it('Obtaining a batch of messages from the outbox should remove them from the outbox', async () => {
        //Given
        const outbox = new Outbox(defaultDumpFilePath);
        outbox.addMessages([fakeMessage]);
        outbox.getTraceBatchToSend();

        //When
        const batch = outbox.getTraceBatchToSend();

        //Then
        expect(batch).toEqual([]);
    });

    it('dumping the outbox should override the previous dump file content and empty the current outbox', async () => {
        mockFs({[defaultDumpFilePath]: JSON.stringify({asd: 'test'})});
        const outbox = new Outbox(defaultDumpFilePath);
        await outbox.loadOutboxFromFile();

        outbox.getTraceBatchToSend();
        const finalOutput = [fakeMessage];
        outbox.addMessages(finalOutput);
        await outbox.dumpOutboxToFile();
        await outbox.loadOutboxFromFile();
        const batch = outbox.getTraceBatchToSend();

        expect(batch).toEqual(finalOutput);
    });

    it('dump events and traces from the outbox', async () => {
        //Given
        mockFs({[defaultDumpFilePath]: ''});
        const outbox = new Outbox(defaultDumpFilePath);
        outbox.addMessages([fakeMessage]);

        outbox.addEvents([fakeEvent]);

        //When
        await outbox.dumpOutboxToFile();

        //Then
        const fileExists = await checkFileExists(defaultDumpFilePath);
        expect(fileExists).toBe(true);
    });

    it('should read events from file', async () => {
        //Given
        const fakeEvent = {
            event_type: 'scraper_state_changed',
            client_timestamp: new Date(),
            origin: 'fake',
            operation_id: 'fake',
            body: {
                new_state: 'STARTED',
                triggered_by: 'USER_VIA_ELECTRON',
                source: 'steam_sales',
                account_identifier: 'fake',
                reason: 'test',
                machineInfo: fakeMessage.machine_info
            }
        };
        mockFs({[defaultDumpFilePath]: JSON.stringify(fakeEvent)});

        //When
        const outbox = new Outbox(defaultDumpFilePath);
        await outbox.loadOutboxFromFile();
        const event = outbox.getEventToSend();

        //Then
        expect(event).toEqual(fakeEvent);
    });

    it('throw error when trying to read invalid file', async () => {
        //Given
        mockFs({[defaultDumpFilePath]: 'a'});
        const errorSpy = jest.spyOn(console, 'error').mockImplementation();
        const readFileSpy = jest.spyOn(fs, 'readFile');
        readFileSpy.mockRejectedValueOnce(new Error('EACCES: permission denied'));

        const outbox = new Outbox(defaultDumpFilePath);

        //When
        await outbox.loadOutboxFromFile();

        //Then
        expect(errorSpy).toHaveBeenCalledWith('Error reading outbox file', expect.anything());
    });

    it('should return events in FIFO order (first in, first out)', () => {
        const outbox = new Outbox(defaultDumpFilePath);

        const createEvent = (index: number, state: NewScraperState) => ({
            ...fakeEvent,
            body: {...fakeEvent.body, new_state: state, reason: `event ${index}`}
        });

        const firstEvent = createEvent(1, 'STARTED');
        const secondEvent = createEvent(2, 'FINISHED');
        const thirdEvent = createEvent(3, 'STOPPED');

        outbox.addEvents([firstEvent, secondEvent, thirdEvent]);

        expect(outbox.getEventToSend()).toEqual(firstEvent);
        expect(outbox.getEventToSend()).toEqual(secondEvent);
        expect(outbox.getEventToSend()).toEqual(thirdEvent);
        expect(outbox.getEventToSend()).toBeUndefined();

        const fourthEvent = createEvent(4, 'STARTED');
        const fifthEvent = createEvent(5, 'FINISHED');
        const sixthEvent = createEvent(6, 'STOPPED');

        outbox.addEvents([fourthEvent]);
        outbox.addEvents([fifthEvent]);
        outbox.addEvents([sixthEvent]);

        expect(outbox.getEventToSend()).toEqual(fourthEvent);
        expect(outbox.getEventToSend()).toEqual(fifthEvent);
        expect(outbox.getEventToSend()).toEqual(sixthEvent);
        expect(outbox.getEventToSend()).toBeUndefined();
    });
});
