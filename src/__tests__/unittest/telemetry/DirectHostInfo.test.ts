import {getDirectHostInfo} from '../../../telemetry/scraperService/machineInfo';

describe('DirectHostInfo', () => {
    it('should return correct DirectHostInfo', () => {
        const directHostInfo = getDirectHostInfo();
        expect(directHostInfo).toHaveProperty('arch');
        expect(directHostInfo).toHaveProperty('homedir');
        expect(directHostInfo).toHaveProperty('hostname');
        expect(directHostInfo).toHaveProperty('platform');
        expect(directHostInfo).toHaveProperty('release');
        expect(directHostInfo).toHaveProperty('os_type');
        expect(directHostInfo).toHaveProperty('os_version');
    });
});
0;
