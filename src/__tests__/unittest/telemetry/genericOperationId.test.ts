import * as telemetry from '../../../telemetry/telemetry';
import {mockS2Communication, resetS2Communication} from '../../utils/s2Mocks';

describe('telemetry', () => {
    let mockScheduleTrace;

    beforeEach(() => {
        mockScheduleTrace = mockS2Communication().scheduleTrace;
    });

    afterEach(() => {
        jest.resetAllMocks();
        resetS2Communication();
    });

    it('should use generic operation id if not provided for trace', () => {
        telemetry.trace('test message');

        expect(mockScheduleTrace).toHaveBeenCalledWith(expect.anything(), telemetry.GENERIC_OPERATION_ID, expect.objectContaining({logLevel: expect.any(Number)}));
    });

    it('should use generic operation id if not provided for exception', () => {
        telemetry.exception(new Error('test message'));

        expect(mockScheduleTrace).toHaveBeenCalledWith(
            expect.anything(),
            telemetry.GENERIC_OPERATION_ID,
            expect.objectContaining({logLevel: expect.any(Number), additionalData: expect.anything()})
        );
    });

    it('should use generic operation id if not provided for event', () => {
        telemetry.trace('test event', {additionalData: {something: 'yes'}});

        expect(mockScheduleTrace).toHaveBeenCalledWith(
            expect.anything(),
            telemetry.GENERIC_OPERATION_ID,
            expect.objectContaining({logLevel: expect.any(Number), additionalData: expect.objectContaining({something: 'yes'})})
        );
    });
});
