import {promises as fs} from 'fs';
import * as path from 'path';
import * as mockFs from 'mock-fs';
import * as Scraper<PERSON><PERSON> from '../../../api/ScraperApi';
import {DependenciesManager} from '../../../dependencies/DependenciesManager';
import {getChromiumId, getScraperId} from '../../../dependencies/identifiers';
import {SourceDependencies} from '../../../dependencies/types';
import * as telemetry from '../../../telemetry/telemetry';
import {ensureDirExists} from '../../../utils/fileUtils';
import * as fileUtils from '../../../utils/fileUtils';
import {executableName, getPlatform} from '../../../utils/platformUtils';
import {getFreshDependenciesManager, scraperLibMainDirectory} from '../../utils/helpers';

const dependenciesManager = getFreshDependenciesManager();

const scrapers1 = '1.0.1';
const scrapers2 = '2.2.0-feature-X';
const scrapers3 = '3.1.4';
const scrapersId1 = getScraperId(scrapers1);
const scrapersId2 = getScraperId(scrapers2);
const scrapersId3 = getScraperId(scrapers3);

const oldChromiumVersion = 729994;
const oldChromiumVersionId = getChromiumId(oldChromiumVersion);
const newChromiumVersion = 970485;
const newChromiumVersionId = getChromiumId(newChromiumVersion);

const dependencyInfoResponse = (scrapers: string, shadowMode = false): SourceDependencies => ({
    default: ScraperApi.createScraperDependencyObject(scrapers, newChromiumVersion, shadowMode)
});

const binsTestPath = `${scraperLibMainDirectory}/binaries`;

const getDependenciesInfoSpy = jest.spyOn(ScraperApi, 'getDependenciesInfo');
const isDependencyWorkingSpy = jest.spyOn(dependenciesManager, 'isDependencyWorking');
const downloadFileSpy = jest.spyOn(fileUtils, 'downloadFile');
const unZipFileAndDeleteSpy = jest.spyOn(fileUtils, 'unZipFileAndDelete');
const moveFileSpy = jest.spyOn(fileUtils, 'moveFile');
const telemetryTraceSpy = jest.spyOn(telemetry, 'trace');

const {platform, arch} = getPlatform();

function mockLocalDependencies(scrapersIds: string[], chromiumIds: string[], additionalFiles?: Record<string, any>) {
    mockFs({
        [binsTestPath]: {
            scrapers: Object.fromEntries(scrapersIds.map((id) => [id, {scrape: ''}])),
            chromium: Object.fromEntries(chromiumIds.map((id) => [id, {}]))
        },
        ...additionalFiles
    });
}

describe('Dependencies Manager', () => {
    beforeEach(() => {
        getDependenciesInfoSpy.mockResolvedValue(dependencyInfoResponse(scrapers3));
        isDependencyWorkingSpy.mockImplementation(() => Promise.resolve(true));
        downloadFileSpy.mockImplementation(async (url: string, dirPath: string, _shouldSkipEmittingEvents: boolean) => {
            await ensureDirExists(dirPath);
            const fileName = fileUtils.getFileNameFromURL(url);
            const downloadedFilePath = path.join(dirPath, fileName);
            await fs.writeFile(downloadedFilePath, 'xyz');
            return downloadedFilePath;
        });
        unZipFileAndDeleteSpy.mockImplementation(async (filePath: string, destinationPath: string) => {
            await ensureDirExists(destinationPath);
            await fs.unlink(filePath);
        });
    });

    afterEach(() => {
        mockFs.restore();
        jest.resetAllMocks();
    });

    describe('getting a list of missing dependencies', () => {
        test('with no dependencies earlier downloaded', async () => {
            mockLocalDependencies([], []);
            expect(await dependenciesManager.getMissingDependenciesList()).toStrictEqual([
                {
                    downloadUrl: `https://stscraperbinstorag01db.blob.core.windows.net/prod-scraper-client/bin/${executableName(scrapersId3)}`,
                    type: 'scrapers',
                    id: scrapersId3,
                    shadowMode: false
                },
                {
                    downloadUrl: `https://storage.googleapis.com/chromium-browser-snapshots/${arch}/${newChromiumVersion}/chrome-${platform}.zip`,
                    type: 'chromium',
                    id: newChromiumVersionId,
                    shadowMode: false
                }
            ]);
        });

        test('with other dependencies earlier downloaded', async () => {
            mockLocalDependencies([scrapersId1, scrapersId2], [oldChromiumVersionId]);
            expect(await dependenciesManager.getMissingDependenciesList()).toStrictEqual([
                {
                    downloadUrl: `https://stscraperbinstorag01db.blob.core.windows.net/prod-scraper-client/bin/${executableName(scrapersId3)}`,
                    type: 'scrapers',
                    id: scrapersId3,
                    shadowMode: false
                },
                {
                    downloadUrl: `https://storage.googleapis.com/chromium-browser-snapshots/${arch}/${newChromiumVersion}/chrome-${platform}.zip`,
                    type: 'chromium',
                    id: newChromiumVersionId,
                    shadowMode: false
                }
            ]);
        });

        test('with only missing dependency of one type', async () => {
            mockLocalDependencies([scrapersId2], [oldChromiumVersionId, newChromiumVersionId]);
            expect(await dependenciesManager.getMissingDependenciesList()).toStrictEqual([
                {
                    downloadUrl: `https://stscraperbinstorag01db.blob.core.windows.net/prod-scraper-client/bin/${executableName(scrapersId3)}`,
                    type: 'scrapers',
                    id: scrapersId3,
                    shadowMode: false
                }
            ]);
        });
    });

    test('I can download missing binaries', async () => {
        mockLocalDependencies([], []);
        await dependenciesManager.syncDependencies();
        expect(downloadFileSpy).toHaveBeenCalledTimes(2);
        expect(await fs.readdir(`${binsTestPath}/scrapers`)).toEqual([scrapersId3]);
        expect(await fs.readdir(`${binsTestPath}/chromium`)).toEqual([newChromiumVersionId]);
    });

    test('Binaries are not downloaded again if they are in locally', async () => {
        mockLocalDependencies([scrapersId3], [newChromiumVersionId]);
        await dependenciesManager.syncDependencies();
        expect(downloadFileSpy).toHaveBeenCalledTimes(0);
    });

    test('Remove all binaries that are not required', async () => {
        const scrapers4 = '4.0.0';

        mockLocalDependencies([scrapersId3], [newChromiumVersionId]);
        getDependenciesInfoSpy.mockResolvedValue(dependencyInfoResponse(scrapers4));

        await dependenciesManager.syncDependencies();
        expect(await fs.readdir(`${binsTestPath}/scrapers`)).toEqual([getScraperId(scrapers4)]);
        expect(await fs.readdir(`${binsTestPath}/chromium`)).toEqual([newChromiumVersionId]);
        expect(downloadFileSpy).toHaveBeenCalledTimes(1);
    });

    test('Concurrent syncDependencies should download binaries once', async () => {
        mockLocalDependencies([], []);
        await Promise.all([
            dependenciesManager.syncDependencies(),
            dependenciesManager.syncDependencies(),
            dependenciesManager.syncDependencies(),
            dependenciesManager.syncDependencies(),
            dependenciesManager.syncDependencies()
        ]);
        expect(downloadFileSpy).toHaveBeenCalledTimes(2); // 1 per each dependency type
    });

    test('syncDependencies should populate requiredDependencies', async () => {
        mockLocalDependencies([], []);
        await dependenciesManager.syncDependencies();
        expect(DependenciesManager.sourceDependencies).toStrictEqual({
            default: {
                id: scrapersId3,
                shadowMode: false,
                type: 'scrapers',
                downloadUrl: `https://stscraperbinstorag01db.blob.core.windows.net/prod-scraper-client/bin/${executableName(scrapersId3)}`,
                dependencies: [
                    {
                        id: newChromiumVersionId,
                        shadowMode: false,
                        type: 'chromium',
                        downloadUrl: `https://storage.googleapis.com/chromium-browser-snapshots/${arch}/${newChromiumVersion}/chrome-${platform}.zip`
                    }
                ]
            }
        });
    });

    test('should move provided archive', async () => {
        const pathToMockChromiumDir = 'preProvidedChromiumFolder';
        moveFileSpy.mockImplementation(async (filePath: string, destinationPath: string) => {
            expect(filePath).toEqual('preProvidedChromiumFolder/chromium-linux-970485');
            expect(destinationPath).toEqual('.private/binaries/chromium/chromium-linux-970485');
        });

        mockLocalDependencies([], [], {preProvidedChromiumFolder: {'chromium-linux-970485': 'chromium-linux-970485'}});
        await dependenciesManager.movePreProvidedChromium(pathToMockChromiumDir);
        expect(moveFileSpy).toBeCalledTimes(1);
    });

    test('should not move provided chromium when it is already available', async () => {
        const pathToMockChromiumDir = 'preProvidedChromiumFolder';
        mockLocalDependencies([], ['chromium-linux-970485'], {preProvidedChromiumFolder: {'chromium-linux-970485': 'chromium-linux-970485'}});

        await dependenciesManager.movePreProvidedChromium(pathToMockChromiumDir);

        // check that we discovered the zip
        expect(telemetryTraceSpy.mock.calls[0][0]).toContain('Discovered preprovided Chromium');
        expect(telemetryTraceSpy.mock.calls[0][0]).toContain('preProvidedChromiumFolder/chromium-linux-970485');
        // but we didn't move it as we found the existing dependency
        expect(moveFileSpy).toBeCalledTimes(0);
    });

    test('should not return chromium path if not given directory', async () => {
        const pathToMockChromiumDir = './preProvidedChromiumFolder/some-other-file';
        mockLocalDependencies([], [], {
            preProvidedChromiumFolder: {'chromium-linux-970485': 'chromium-linux-970485', 'some-other-file': 'some-other-file'}
        });
        await dependenciesManager.movePreProvidedChromium(pathToMockChromiumDir);
        expect(telemetryTraceSpy.mock.calls[0][0]).toContain('Could not locate Chromium');
    });

    test('should not return chromium path if directory does not contain chromium', async () => {
        const pathToMockChromiumDir = './preProvidedChromiumFolder';
        mockLocalDependencies([], [], {preProvidedChromiumFolder: {'chromiumNot-version': 'chromium-version'}});
        await dependenciesManager.movePreProvidedChromium(pathToMockChromiumDir);
        expect(telemetryTraceSpy.mock.calls[0][0]).toContain('No Chromium binary');
    });

    test('should return one path if directory contains multiple versions', async () => {
        const pathToMockChromiumDir = './preProvidedChromiumFolder';
        moveFileSpy.mockImplementation(async (filePath: string) => {
            expect(filePath).toEqual('preProvidedChromiumFolder/chromium-linux-970485');
        });
        mockLocalDependencies([], [], {
            preProvidedChromiumFolder: {'chromium-linux-970485': 'chromium-linux-970485', 'chromium-linux-970486': 'chromium-linux-970486'}
        });
        await dependenciesManager.movePreProvidedChromium(pathToMockChromiumDir);
    });

    test('should skip downloading shadowMode binaries', async () => {
        mockLocalDependencies([], []);
        getDependenciesInfoSpy.mockResolvedValue(dependencyInfoResponse(scrapers1, true));
        await dependenciesManager.syncDependencies(true);
        expect(downloadFileSpy).toHaveBeenCalledTimes(0);

        // TODO: refactor dependencyInfoResponse, so it could return dependencies for multiple sources
        getDependenciesInfoSpy.mockResolvedValue(dependencyInfoResponse(scrapers3, false));
        await dependenciesManager.syncDependencies(true);
        expect(downloadFileSpy).toHaveBeenCalledTimes(2);
    });

    test('should skip emitting events for shadowBinaries', async () => {
        mockLocalDependencies([], []);
        getDependenciesInfoSpy.mockResolvedValue(dependencyInfoResponse(scrapers1, true));
        await dependenciesManager.syncDependencies();
        expect(downloadFileSpy).toBeCalledTimes(2);
        expect(downloadFileSpy.mock.calls[0]).toEqual([expect.anything(), expect.anything(), true]);
        expect(downloadFileSpy.mock.calls[1]).toEqual([expect.anything(), expect.anything(), true]);
    });

    test('should throw dependency sync error', async () => {
        mockLocalDependencies([], []);
        getDependenciesInfoSpy.mockImplementation(() => new Promise((_, reject) => setTimeout(() => reject(new Error()), 500)));

        const promises: Promise<any>[] = [
            dependenciesManager.syncDependencies(false, true),
            new Promise((resolve) => setTimeout(() => resolve(dependenciesManager.syncDependencies(false, true)), 100))
        ];
        await expect(Promise.all(promises)).rejects.toThrowError('DEPENDENCIES_SYNC_ERROR');
        expect(getDependenciesInfoSpy).toBeCalledTimes(1);
        await expect(dependenciesManager.syncDependencies(false, true)).rejects.toThrowError('DEPENDENCIES_SYNC_ERROR');
        expect(getDependenciesInfoSpy).toBeCalledTimes(2);
    });
});
