import * as child_process from 'child_process';
import {DependenciesManager} from '../../../dependencies/DependenciesManager';
import {getScraperId} from '../../../dependencies/identifiers';
import {Dependency, DependencyType} from '../../../dependencies/types';
import * as pauseUtils from '../../../utils/pause';
import {getFreshDependenciesManager} from '../../utils/helpers';

const scrapers3 = '3.1.4';

const scrapersId3 = getScraperId(scrapers3);

describe('isDependencyWorking', () => {
    const dependency: Dependency = {type: DependencyType.SCRAPERS, id: scrapersId3};
    let dependenciesManager: DependenciesManager;
    let getDependencyExecPathSpy;

    function mockExecPath(fileName: string) {
        getDependencyExecPathSpy.mockReturnValue(`src/__tests__/unittest/dependencies/examples/${fileName}`);
    }

    beforeEach(() => {
        jest.spyOn(pauseUtils, 'pause').mockImplementation(() => Promise.resolve());
        dependenciesManager = getFreshDependenciesManager();
        getDependencyExecPathSpy = jest.spyOn(dependenciesManager, 'getDependencyExecPath');
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    test('should be able to confirm working dependency', async () => {
        mockExecPath('good.sh');
        expect(await dependenciesManager.isDependencyWorking(dependency)).toBe(true);
    });

    test('should be able to return false for dependency which does not exists', async () => {
        mockExecPath('non_existing.sh');
        expect(await dependenciesManager.isDependencyWorking(dependency)).toBe(false);
    });

    test('should be able to detect not working dependency which prints to stderr but exits with 0', async () => {
        mockExecPath('bad_exit0.sh');
        expect(await dependenciesManager.isDependencyWorking(dependency)).toBe(false);
    });

    test('should be able to return false for dependency that exits with 1', async () => {
        mockExecPath('bad_exit1.sh');
        expect(await dependenciesManager.isDependencyWorking(dependency)).toBe(false);
    });

    test('should retry in case of exception while running the dependency', async () => {
        mockExecPath('bad_exit1.sh');
        const execSpy = jest.spyOn(child_process, 'exec');
        expect(await dependenciesManager.isDependencyWorking(dependency)).toBe(false);
        expect(execSpy.mock.calls.length).toBe(3);
    });
});
