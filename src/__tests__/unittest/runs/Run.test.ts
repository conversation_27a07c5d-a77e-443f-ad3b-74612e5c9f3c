import {errorType} from '../../../configurations/errorType';
import {ScraperLibError} from '../../../processes/types/errors';
import {BaseContext} from '../../../runs/jobs/context';
import {OperationTerminated, Run} from '../../../runs/Run';
import {TriggeredBy} from '../../../telemetry/scraperService/scraperServiceEvents';
import {Command, Source} from '../../../types';
import {nextEventLoopTick} from '../../../utils/asyncUtils';
import {Deferred} from '../../../utils/Deferred';
import {TestJob} from '../../utils/TestJob';

let executeSpy: jest.SpyInstance;
let onSuccessSpy: jest.SpyInstance;
let onFailSpy: jest.SpyInstance;

async function sleep(number: number) {
    return new Promise((resolve) => setTimeout(resolve, number));
}

describe('Run', () => {
    beforeEach(async () => {
        executeSpy = jest.spyOn(TestJob.prototype, 'execute');
        onSuccessSpy = jest.spyOn(TestJob.prototype, 'onSuccess');
        onFailSpy = jest.spyOn(TestJob.prototype, 'onFail');
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    test('should wait for results of all jobs with waitForResult', async () => {
        const job1Result = 2;
        const job2Result = '4';
        const job3Result = true;
        const run = new Run([
            new TestJob(async () => {
                await sleep(300);
                return job1Result;
            }),
            new TestJob(async () => {
                await sleep(200);
                return job2Result;
            }),
            new TestJob(async () => {
                await sleep(100);
                return job3Result;
            })
        ]);
        expect(run.isDone).toBe(false);
        const executeResult = run.executeAllJobs(); //Not waiting deliberately since getJobResults should wait for all jobs to finish
        const jobResults = await run.getJobResults();
        expect(jobResults).toEqual([job1Result, job2Result, job3Result]);
        await executeResult;
    });

    test('Run.execute executes all jobs', async () => {
        const run = new Run([new TestJob(), new TestJob(), new TestJob()]);
        expect(run.isDone).toBe(false);
        await run.executeAllJobs();
        expect(run.isDone).toBe(true);
        expect(executeSpy).toHaveBeenCalledTimes(3);
        expect(onSuccessSpy).toHaveBeenCalledTimes(3);
        expect(onFailSpy).toHaveBeenCalledTimes(0);
    });

    test('First fail prevents next jobs from being executed', async () => {
        const message = 'fail!';
        const run = new Run([
            new TestJob(),
            new TestJob(async () => {
                throw new Error(message);
            }),
            new TestJob()
        ]);
        expect(run.isDone).toBe(false);
        await expect(async () => await run.executeAllJobs()).rejects.toThrow(message);
        expect(run.isDone).toBe(false);
        expect(executeSpy).toHaveBeenCalledTimes(2);
        expect(onSuccessSpy).toHaveBeenCalledTimes(1);
        expect(onFailSpy).toHaveBeenCalledTimes(1);
    });

    test('Killed Run do not starts executes jobs', async () => {
        const run = new Run([new TestJob(), new TestJob(), new TestJob()]);
        await run.kill();
        await run.executeAllJobs();
        expect(run.isDone).toBe(false);
        expect(executeSpy).toHaveBeenCalledTimes(0);
        expect(onSuccessSpy).toHaveBeenCalledTimes(0);
        expect(onFailSpy).toHaveBeenCalledTimes(0);
    });

    test("Killed run's getJobResults, rejects with object of OperationTerminated class", async () => {
        const run = new Run([new TestJob(), new TestJob(), new TestJob()]);
        await run.kill();
        await expect(run.getJobResults()).rejects.toThrow(new OperationTerminated());
    });

    test('run.kill kills currentJob and prevent next jobs from execution', async () => {
        class KillableJob extends TestJob {
            deferred = new Deferred();

            constructor() {
                super(async () => {
                    await this.deferred.promise;
                });
            }

            async kill() {
                this.deferred.resolve();
            }
        }

        const killSpy = jest.spyOn(KillableJob.prototype, 'kill');

        const job1 = new KillableJob();
        const job2 = new TestJob();
        const run = new Run([job1, job2]);

        const promise = run.executeAllJobs();
        expect(job1.started).toBe(true);
        expect(job1.finished).toBe(false);
        expect(job2.started).toBe(false);

        await run.kill();
        expect(killSpy).toHaveBeenCalledTimes(1);
        await promise;
        expect(job1.finished).toBe(true);
        expect(job2.started).toBe(false);
    });

    test('run passes context from job to job', async () => {
        const run = new Run(
            [
                new TestJob(async (context) => {
                    expect(context.test).toEqual(0);
                    context.test++;
                }),
                new TestJob(async (context) => {
                    expect(context.test).toEqual(1);
                })
            ],
            {
                context: {
                    test: 0,
                    source: Source.STEAM_SALES,
                    command: Command.SCRAPE,
                    isShadowRun: false,
                    triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
                    forceRunStart: false,
                    operationId: 'fake'
                } as BaseContext
            }
        );
        await run.executeAllJobs();
        expect(executeSpy).toHaveBeenCalledTimes(2);
    });

    test('If all jobs succeeds, run.onSuccess is called if provided', async () => {
        const onSuccess = jest.fn();
        const run = new Run([new TestJob(), new TestJob()]);
        run.setEventHandlers({onSuccess});
        await run.executeAllJobs();
        expect(onSuccess).toHaveBeenCalledTimes(1);
    });

    test('if specified onFinally should run after a successful run', async () => {
        let testCounter = 0;

        const run = new Run([new TestJob(async () => void 0)]);
        run.setEventHandlers({
            onFinally: async () => {
                testCounter++;
                return;
            }
        });
        await run.executeAllJobs();
        expect(testCounter).toEqual(1);
    });

    test('if specified onFinally should run after a failed run', async () => {
        let testCounter = 0;

        const run = new Run([
            new TestJob(async () => {
                throw Error('fail!');
            })
        ]);
        run.setEventHandlers({
            onFinally: async () => {
                testCounter++;
                return;
            }
        });
        await expect(run.executeAllJobs()).rejects.toThrow('fail!');
        expect(testCounter).toEqual(1);
    });

    describe('executeAllJobs can retry jobs', () => {
        test('should execute job only once when no retry options are specified', async () => {
            const job = new TestJob(async () => {
                throw Error('fail!');
            });
            const run = new Run([job]);
            await expect(run.executeAllJobs()).rejects.toThrow('fail!');
            expect(job.retryOptions.maxAttempts).toEqual(1);
            expect(executeSpy).toHaveBeenCalledTimes(1);
            expect(onSuccessSpy).toHaveBeenCalledTimes(0);
            expect(onFailSpy).toHaveBeenCalledTimes(1);
            expect(run.isDone).toBe(false);
        });

        test('should mark job as successful if it succeeds on any retry attempt within maxAttempts limit', async () => {
            class CustomRetriableTestJob extends TestJob {
                retryOptions = {maxAttempts: 5};
            }

            let attempt = 0;
            const job = new CustomRetriableTestJob(async () => {
                attempt++;
                if (attempt == 1 || attempt == 2) {
                    throw Error('fail!');
                }
                return 'success';
            });

            const run = new Run([job]);
            const executeResult = run.executeAllJobs();
            const jobResults = await run.getJobResults();
            expect(jobResults).toEqual(['success']);
            expect(executeSpy).toHaveBeenCalledTimes(3);
            expect(onSuccessSpy).toHaveBeenCalledTimes(1);
            expect(onFailSpy).toHaveBeenCalledTimes(0);
            expect(run.isDone).toBe(true);
            await executeResult;
        });

        test('should retry job exactly maxAttempts times before failing', async () => {
            class CustomRetriableTestJob extends TestJob {
                retryOptions = {maxAttempts: 5};
            }

            const job = new CustomRetriableTestJob(async () => {
                throw Error('fail!');
            });

            const run = new Run([job]);
            await expect(run.executeAllJobs()).rejects.toThrow('fail!');
            expect(executeSpy).toHaveBeenCalledTimes(job.retryOptions.maxAttempts);
            expect(onSuccessSpy).toHaveBeenCalledTimes(0);
            expect(onFailSpy).toHaveBeenCalledTimes(1);
            expect(run.isDone).toBe(false);
        });

        test('should not retry job after run is killed even if maxAttempts allows more retries', async () => {
            class CustomRetriableTestJob extends TestJob {
                retryOptions = {maxAttempts: 5};
            }

            let attempt = 0;
            const attemptDuringWhichRunIsKilled = 3;
            const deferredFail = new Deferred();
            const job = new CustomRetriableTestJob(async () => {
                attempt++;
                if (attempt === attemptDuringWhichRunIsKilled) {
                    await deferredFail.promise; // we are waiting here, so we could have a chance to kill the run, before next retry.
                }
                throw Error('fail!');
            });

            const run = new Run([job]);
            const executeAllJobsPromise = run.executeAllJobs();
            await nextEventLoopTick();
            expect(executeSpy).toHaveBeenCalledTimes(attemptDuringWhichRunIsKilled);

            await run.kill();
            deferredFail.resolve();

            await expect(executeAllJobsPromise).rejects.toThrow(new ScraperLibError(errorType.JOB_STOPPED_BY_USER));
            expect(executeSpy).toHaveBeenCalledTimes(attemptDuringWhichRunIsKilled);
            expect(run.isDone).toBe(false);
        });
    });
});
