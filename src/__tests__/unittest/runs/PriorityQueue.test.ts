import PriorityQueue from '../../../runs/PriorityQueue';
import * as telemetry from '../../../telemetry/telemetry';
import {Command, Source} from '../../../types';
import {createRun, getJobAndItsDeferredPromise} from '../../utils/RunUtils';

describe('PriorityQueue should', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });

    test('start a run automatically if there is room in the processing que', async () => {
        //Given
        const priorityQueue = new PriorityQueue(1);

        //When
        const testRun = createRun([getJobAndItsDeferredPromise().job]);
        priorityQueue.add(testRun);

        //Then
        expect(priorityQueue.getQueue()).toHaveLength(0);
        expect(priorityQueue.getCurrentlyRunning()).toHaveLength(1);
    });

    const runLimit = 3;
    test('set proper waiting and in progress statuses on jobs', async () => {
        //Given
        const jobAmount = 5;
        const priorityQueue = new PriorityQueue(runLimit, 100000);

        for (let i = 0; i < jobAmount; i++) {
            priorityQueue.add(createRun([getJobAndItsDeferredPromise().job], {priority: i}));
        }

        //When
        priorityQueue.fillUpRunningQueue();

        //Then
        const waitingForProcessingQueue = priorityQueue.getQueue();
        const currentlyRunning = priorityQueue.getCurrentlyRunning();

        expect(waitingForProcessingQueue.length).toEqual(jobAmount - runLimit);
        expect(currentlyRunning.length).toEqual(runLimit);
        waitingForProcessingQueue.forEach((run) => {
            expect(run.priority).toBeGreaterThanOrEqual(runLimit);
        });
        currentlyRunning.forEach((run) => {
            expect(run.priority).toBeLessThan(runLimit);
        });
    });

    test('start a job with forceJobRun flag immediately even if the queue is full', async () => {
        //Given
        const priorityQueue = new PriorityQueue(1);
        priorityQueue.add(createRun([getJobAndItsDeferredPromise().job]));

        //When
        const testRun = createRun([getJobAndItsDeferredPromise().job], {forceJobRun: true});
        priorityQueue.add(testRun);

        //Then
        expect(priorityQueue.getQueue()).toHaveLength(0);
        expect(priorityQueue.getCurrentlyRunning()).toHaveLength(2);
        expect(testRun.isActive()).toBeTruthy();
    });

    test('should make sure that runs with higher priority are on top of the waiting list if running limit is full', async () => {
        //Given
        const lowPriorityValue = 1;
        const highPriorityValue = 10;

        const priorityQueue = new PriorityQueue(1);
        const jobWithLowPriority = createRun([getJobAndItsDeferredPromise().job], {priority: lowPriorityValue});
        const jobWithHighPriority = createRun([getJobAndItsDeferredPromise().job], {priority: highPriorityValue});

        // fill the running queue
        priorityQueue.add(createRun([getJobAndItsDeferredPromise().job]));

        //When
        priorityQueue.add(jobWithLowPriority);
        priorityQueue.add(jobWithHighPriority);

        //Then
        const waitingForProcessingQueue = priorityQueue.getQueue();
        expect(waitingForProcessingQueue[0].priority).toEqual(highPriorityValue);
        expect(waitingForProcessingQueue[1].priority).toEqual(lowPriorityValue);
    });

    test('kill all running runs and clears the queue from pending runs', async () => {
        //Given
        const priorityQueue = new PriorityQueue(1);
        priorityQueue.add(createRun([getJobAndItsDeferredPromise().job]));
        priorityQueue.add(createRun([getJobAndItsDeferredPromise().job]));

        //When
        await priorityQueue.killAll();

        //Then
        expect(priorityQueue.getQueue()).toHaveLength(0);
        expect(priorityQueue.getCurrentlyRunning()).toHaveLength(0);
    });

    test('kill running runs and clear the queue from pending runs with matching specified source', async () => {
        //Given
        const priorityQueue = new PriorityQueue(1);
        const sourceThatShouldNotBeKIlled = Source.NINTENDO_SALES;
        const sourceToKill = Source.STEAM_SALES;
        priorityQueue.add(createRun([getJobAndItsDeferredPromise().job], {source: sourceToKill}));
        priorityQueue.add(createRun([getJobAndItsDeferredPromise().job], {source: sourceThatShouldNotBeKIlled}));
        priorityQueue.add(createRun([getJobAndItsDeferredPromise().job], {source: sourceToKill}));

        //When
        await priorityQueue.killBySourceAndCommand(sourceToKill, Command.SCRAPE);

        //Then
        expect(priorityQueue.getQueue()).toHaveLength(0);
        const currentlyRunning = priorityQueue.getCurrentlyRunning();
        expect(currentlyRunning).toHaveLength(1);
        expect(currentlyRunning[0].context.source).toEqual(sourceThatShouldNotBeKIlled);
    });

    const testErrorMessage = 'Test error';

    test('handle case in which one run fails by throwing an error', async () => {
        //Given
        const priorityQueue = new PriorityQueue(3);

        const jobWithDeferred1 = getJobAndItsDeferredPromise();
        const jobWithDeferred2 = getJobAndItsDeferredPromise();
        const jobWithDeferred3 = getJobAndItsDeferredPromise();

        const deferred1 = jobWithDeferred1.deferred;
        const deferred2 = jobWithDeferred2.deferred;
        const deferred3 = jobWithDeferred3.deferred;

        const testRun1 = createRun([jobWithDeferred1.job], {source: Source.STEAM_SALES});
        const testRun2 = createRun([jobWithDeferred2.job], {source: Source.NINTENDO_SALES});
        const testRun3 = createRun([jobWithDeferred3.job], {source: Source.GOG_SALES});

        priorityQueue.add(testRun1);
        priorityQueue.add(testRun2);
        priorityQueue.add(testRun3);

        const telemetryExceptionSpy = jest.spyOn(telemetry, 'exception');
        const telemetryTraceSpy = jest.spyOn(telemetry, 'trace');

        // When
        deferred1.resolve();
        deferred2.reject(new Error(testErrorMessage));
        deferred3.resolve();

        await testRun1.getJobResults();
        try {
            await testRun2.getJobResults();
        } catch {
            /* do nothing since this fails as expected!*/
        }
        await testRun3.getJobResults();

        // Then
        expect(telemetryExceptionSpy).toHaveBeenCalledWith(new Error(testErrorMessage), true, {operation: 'run'});
        expect(telemetryTraceSpy).toHaveBeenCalledWith('Run failed');

        expect(jobWithDeferred1.job.finished).toBe(true);
        expect(jobWithDeferred2.job.finished).toBe(false);
        expect(jobWithDeferred3.job.finished).toBe(true);
    });

    test('handle case in which we do not add a run to a runningQueue because of related source, as long as related source is running', async () => {
        const priorityQueue = new PriorityQueue(3);

        const jobWithDeferred1 = getJobAndItsDeferredPromise();
        const jobWithDeferred2 = getJobAndItsDeferredPromise();
        const jobWithDeferred3 = getJobAndItsDeferredPromise();
        const jobWithDeferred4 = getJobAndItsDeferredPromise();

        const testRun1 = createRun([jobWithDeferred1.job], {source: Source.NINTENDO_SALES});
        const testRun2 = createRun([jobWithDeferred2.job], {source: Source.GOG_SALES});
        const testRun3 = createRun([jobWithDeferred3.job], {source: Source.NINTENDO_DISCOUNTS});
        const testRun4 = createRun([jobWithDeferred4.job], {source: Source.APP_STORE_SALES});

        priorityQueue.add(testRun1);
        priorityQueue.add(testRun2);
        priorityQueue.add(testRun3);
        priorityQueue.add(testRun4);

        priorityQueue.fillUpRunningQueue();

        expect(priorityQueue.getCurrentlyRunning()).toEqual([testRun1, testRun2, testRun4]);
        expect(priorityQueue.getQueue()).toEqual([testRun3]);

        jobWithDeferred1.deferred.resolve();
        await testRun1.getJobResults();

        priorityQueue.fillUpRunningQueue();
        expect(priorityQueue.getQueue()).toHaveLength(0);
        expect(priorityQueue.getCurrentlyRunning()).toEqual([testRun2, testRun4, testRun3]);
    });
});
