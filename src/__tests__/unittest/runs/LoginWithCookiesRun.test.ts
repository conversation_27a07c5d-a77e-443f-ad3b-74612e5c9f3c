import {errorType} from '../../../configurations/errorType';
import {TriggeredBy} from '../../../telemetry/scraperService/scraperServiceEvents';
import {Source} from '../../../types';
import * as fileUtils from '../../../utils/fileUtils';
import {mockScraperServiceClient} from '../../utils/helpers';
import {createLoginWithCookiesRun, getRemoveFileOrDirectorySpy} from './jobs/loadCookies.helper';

describe('LoginWithCookiesRun', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('onScheduled should send SCHEDULED event to S2', async () => {
        const sendLoginStateChangedEventSpy = jest.spyOn(mockScraperServiceClient, 'scheduleLoginStateChangedEvent');
        const run = createLoginWithCookiesRun();

        await run.onScheduled!();

        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'SCHEDULED',
            isManualSession: true,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined
        });
    });

    it('onStart should send STARTED event to S2', async () => {
        const sendLoginStateChangedEventSpy = jest.spyOn(mockScraperServiceClient, 'scheduleLoginStateChangedEvent');
        const run = createLoginWithCookiesRun();

        await run.onStart!();

        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'STARTED',
            isManualSession: true,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined
        });
    });

    it('onSuccess should send CONFIGURED event to S2', async () => {
        const sendLoginStateChangedEventSpy = jest.spyOn(mockScraperServiceClient, 'scheduleLoginStateChangedEvent');
        const run = createLoginWithCookiesRun();

        await run.onSuccess!();

        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'CONFIGURED',
            isManualSession: true,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined
        });
    });

    it('onFail should send FAILED event to S2 with errorType', async () => {
        const sendLoginStateChangedEventSpy = jest.spyOn(mockScraperServiceClient, 'scheduleLoginStateChangedEvent');
        jest.spyOn(fileUtils, 'removeFileOrDirectory').mockResolvedValue(undefined);
        const run = createLoginWithCookiesRun();
        const error = {errorType: errorType.DEPENDENCIES_SYNC_ERROR};

        await run.onFail!(error);

        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'FAILED',
            isManualSession: true,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined,
            reason: errorType.DEPENDENCIES_SYNC_ERROR
        });
    });

    it('onFail should send FAILED event to S2 with default errorType if none provided', async () => {
        const sendLoginStateChangedEventSpy = jest.spyOn(mockScraperServiceClient, 'scheduleLoginStateChangedEvent');
        jest.spyOn(fileUtils, 'removeFileOrDirectory').mockResolvedValue(undefined);
        const run = createLoginWithCookiesRun();
        const error = {};

        await run.onFail!(error);

        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'FAILED',
            isManualSession: true,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined,
            reason: errorType.UNEXPECTED_ERROR
        });
    });

    it('onFail should remove sessionPath with throwOnFail=false', async () => {
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory').mockResolvedValue(undefined);
        const run = createLoginWithCookiesRun();
        const error = {};

        await run.onFail!(error);

        expect(removeFileOrDirectorySpy).toBeCalledWith('fake/session/path', false);
    });

    it('onKill should send STOPPED event to S2', async () => {
        const sendLoginStateChangedEventSpy = jest.spyOn(mockScraperServiceClient, 'scheduleLoginStateChangedEvent');
        jest.spyOn(fileUtils, 'removeFileOrDirectory').mockResolvedValue(undefined);
        const run = createLoginWithCookiesRun();

        await run.onKill!();

        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'STOPPED',
            isManualSession: true,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined
        });
    });

    it('onKill should remove sessionPath with throwOnFail=false', async () => {
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory').mockResolvedValue(undefined);
        const run = createLoginWithCookiesRun({
            sessionPath: 'custom/session/path.json'
        });

        await run.onKill!();

        expect(removeFileOrDirectorySpy).toBeCalledWith('custom/session/path.json', false);
    });

    it('onFail should attempt file removal even when sessionPath is undefined', async () => {
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory').mockResolvedValue(undefined);
        const run = createLoginWithCookiesRun({
            sessionPath: undefined
        });
        const error = {};

        await run.onFail!(error);

        expect(removeFileOrDirectorySpy).toHaveBeenCalledWith(undefined, false);
    });

    it('onKill should attempt file removal even when sessionPath is undefined', async () => {
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory').mockResolvedValue(undefined);
        const run = createLoginWithCookiesRun({
            sessionPath: undefined
        });

        await run.onKill!();

        expect(removeFileOrDirectorySpy).toHaveBeenCalledWith(undefined, false);
    });

    it('onFail should handle file removal errors gracefully due to throwOnFail=false', async () => {
        const removeFileOrDirectorySpy = getRemoveFileOrDirectorySpy();
        const sendLoginStateChangedEventSpy = jest.spyOn(mockScraperServiceClient, 'scheduleLoginStateChangedEvent');
        const run = createLoginWithCookiesRun();
        const originalError = {errorType: errorType.UNEXPECTED_ERROR};

        await expect(run.onFail!(originalError)).resolves.toBeUndefined();
        expect(removeFileOrDirectorySpy).toBeCalledWith('fake/session/path', false);
        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'FAILED',
            isManualSession: true,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined,
            reason: errorType.UNEXPECTED_ERROR
        });
    });

    it('onKill should handle file removal errors gracefully due to throwOnFail=false', async () => {
        const removeFileOrDirectorySpy = getRemoveFileOrDirectorySpy();
        const sendLoginStateChangedEventSpy = jest.spyOn(mockScraperServiceClient, 'scheduleLoginStateChangedEvent');
        const run = createLoginWithCookiesRun();

        await expect(run.onKill!()).resolves.toBeUndefined();
        expect(removeFileOrDirectorySpy).toBeCalledWith('fake/session/path', false);
        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'STOPPED',
            isManualSession: true,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined
        });
    });
});
