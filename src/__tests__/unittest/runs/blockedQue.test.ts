import {Source} from '../../../types';
import {Deferred} from '../../../utils/Deferred';
import {createRun, testWithRunManagerContext} from '../../utils/RunUtils';
import {TestJob} from '../../utils/TestJob';

function getJobAndItsDefferedPromise() {
    const deferred = new Deferred();
    const job = new TestJob(async () => {
        await deferred.promise;
    });
    return {deferred, job};
}
describe('RunManager', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });

    const runLimit = 3;

    test('recreating electron-app issue with stuck query', async () => {
        await testWithRunManagerContext(async (runManager) => {
            async function addAllSourcesToRunManager() {
                for (const source in Source) {
                    runManager.addRun(createRun([getJobAndItsDefferedPromise().job], {source: source as Source})).catch((e) => console.log(e));
                }
            }
            await addAllSourcesToRunManager();

            const runningRuns = runManager.getRunningRuns();
            expect(runningRuns.length).toBe(runLimit);
            expect(runManager.getScheduledRuns().length).toEqual(Object.keys(Source).length - runLimit);

            for (let i = 0; i < 10; i++) {
                await runManager.killAll();
                expect(runManager.getRunningRuns().length).toBe(0);
                expect(runManager.getScheduledRuns().length).toBe(0);

                await addAllSourcesToRunManager();

                const runningRuns1 = runManager.getRunningRuns();
                expect(runningRuns1.length).toBe(runLimit);
            }
        }, 3);
    });
});
