import {ScraperConfigurationStatus} from '../../../configurations/ScraperConfiguration';
import {ScraperLibEmitterObject} from '../../../emitter';
import {ScrapeResult} from '../../../processes/types';
import {ScraperLib} from '../../../ScraperLib';
import {Source} from '../../../types/Source';
import {createDateRange} from '../../utils/datesHelpers';
import {getFreshLib, testSourceAccountWithParams} from '../../utils/helpers';
import {mockApiCoverage} from '../../utils/scraperApiMocks';
import {mockBinaryScrapeCommand, mockDependencies, mockReportUpload} from '../../utils/scraperRunMocks';

async function getTestScraperLibWithConfigurations() {
    const events: ScraperLibEmitterObject[] = [];
    const scraperLib: ScraperLib = await getFreshLib({
        emitter: (event) => events.push(event),
        shouldLogin: false,
        withMockPriorityQueue: true,
        skipInit: true
    });

    await scraperLib.addScraperConfiguration({source: Source.PLAYSTATION_SALES}, testSourceAccountWithParams());
    await scraperLib.addScraperConfiguration({source: Source.NINTENDO_SALES}, testSourceAccountWithParams());

    return {scraperLib, events};
}

describe('check scraper runs events', () => {
    let events: ScraperLibEmitterObject[];
    let scraperLib: ScraperLib;

    beforeEach(async () => {
        const mocks = await getTestScraperLibWithConfigurations();
        scraperLib = mocks.scraperLib;
        events = mocks.events;
        // Setup mocks
        mockReportUpload();
        mockBinaryScrapeCommand([{} as ScrapeResult]);
        mockDependencies();
        mockApiCoverage([createDateRange()]);
    });

    afterEach(async () => {
        await scraperLib.close();
        jest.restoreAllMocks();
    });

    test('should emit events when scraper runs are started and stopped', async () => {
        await scraperLib.scrapeSource(Source.PLAYSTATION_SALES);
        await scraperLib.scrapeSource(Source.NINTENDO_SALES);

        await scraperLib.stopScrape(Source.PLAYSTATION_SALES);

        const result = events.map(({message}) => JSON.parse(message));

        expect(result).toEqual(
            expect.arrayContaining([
                {
                    source: Source.PLAYSTATION_SALES,
                    newStatus: ScraperConfigurationStatus.SCHEDULED,
                    type: 'scraper-run-update'
                },
                {
                    source: Source.PLAYSTATION_SALES,
                    newStatus: ScraperConfigurationStatus.RUNNING_SCRAPE,
                    type: 'scraper-run-update'
                },
                {
                    source: Source.NINTENDO_SALES,
                    newStatus: ScraperConfigurationStatus.SCHEDULED,
                    type: 'scraper-run-update'
                },
                {
                    source: Source.NINTENDO_SALES,
                    newStatus: ScraperConfigurationStatus.RUNNING_SCRAPE,
                    type: 'scraper-run-update'
                }
            ])
        );
    });
});
