import {SourceCredentials, credentialsToSourceAccount, sourceAccountToCredentials} from '../../../runs/credentials';
import {Source} from '../../../types/Source';

const credentialsArray: Array<{source: Source; credentials: SourceCredentials[Source]}> = [
    {source: Source.PLAYSTATION_SALES, credentials: {clientId: 'id', clientSecret: 'secret'}},
    {source: Source.NINTENDO_SALES, credentials: {user: 'id', password: 'secret', totpSecret: 'xyz'}},
    {source: Source.GOG_SALES, credentials: {user: 'id', password: 'secret'}},
    {source: Source.META_QUEST_SALES, credentials: {user: 'id', password: 'secret', totpSecret: 'xyz'}},
    {source: Source.META_RIFT_SALES, credentials: {user: 'id', password: 'secret', totpSecret: 'xyz'}},
    {source: Source.HUMBLE_SALES, credentials: {user: 'id', password: 'secret', totpSecret: 'xyz'}},
    {source: Source.STEAM_SALES, credentials: {user: 'id', password: 'secret', totpSecret: 'xyz'}},
    {source: Source.STEAM_IMPRESSIONS, credentials: {user: 'id', password: 'secret', totpSecret: 'xyz'}},
    {source: Source.STEAM_WISHLISTS, credentials: {user: 'id', password: 'secret', totpSecret: 'xyz'}},
    {source: Source.MICROSOFT_SALES, credentials: {user: 'id', password: 'secret', apiSetupData: []}},
    {source: Source.EPIC_SALES, credentials: {user: 'id', password: 'secret', loginWith: 'steam', totpSecret: 'xyz'}}
];

describe('credentials', () => {
    test.each(credentialsArray)('credentials - sourceAccount mapping is consistent for %s', async ({source, credentials}) => {
        const sourceAccount = credentialsToSourceAccount(source, credentials as any);
        const newCredentials = sourceAccountToCredentials(source, {id: '', accountIdentifier: '', sessionPath: '', ...sourceAccount});
        expect(credentials).toEqual(newCredentials);
    });

    test('For missing cliParams, sourceAccountToCredentials returns undefined', () => {
        expect(sourceAccountToCredentials(Source.PLAYSTATION_SALES, {id: '', accountIdentifier: '', sessionPath: '', cliParams: undefined})).toBeUndefined();
        expect(sourceAccountToCredentials(Source.PLAYSTATION_SALES, {id: '', accountIdentifier: '', sessionPath: '', cliParams: {}})).toBeUndefined();
    });
});
