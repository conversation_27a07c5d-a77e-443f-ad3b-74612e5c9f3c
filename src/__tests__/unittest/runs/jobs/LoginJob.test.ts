import {BinaryProxy, LoginParams} from '../../../../dependencies/BinaryProxy';
import {BinaryLoginResult} from '../../../../processes/types';
import {Execution} from '../../../../processes/types/Execution';
import {LoginRunContext} from '../../../../runs/jobs/context';
import {LoginJob} from '../../../../runs/jobs/LoginJob';

function getBinaryProxyMock(result: BinaryLoginResult) {
    const killMock = jest.fn().mockResolvedValue(undefined);
    return {
        run: jest.fn().mockResolvedValue({
            kill: killMock as unknown as (operationId: string) => Promise<void>,
            result: Promise.resolve(result as unknown as BinaryLoginResult)
        } as Execution<BinaryLoginResult>),
        killMock
    } as unknown as BinaryProxy & {killMock: jest.Mock<Promise<void>, []>};
}

describe('LoginJob', () => {
    test('LoginJob.kill calls kill on the execution', async () => {
        const binaryProxy = getBinaryProxyMock({} as BinaryLoginResult);
        const loginJob = new LoginJob(binaryProxy, {credentials: {}} as LoginParams);
        const context = {operationId: 'fake'} as LoginRunContext;
        await loginJob.execute(context);

        await loginJob.kill(context);
        expect(binaryProxy.killMock).toHaveBeenCalled();
    });
});
