import {DeleteSessionJob, DeleteSessionJobContext} from '../../../../runs/jobs/DeleteSessionJob';
import {removeFileOrDirectory} from '../../../../utils/fileUtils';

jest.mock('../../../../utils/fileUtils');

const mockedRemoveFileOrDirectory = jest.mocked(removeFileOrDirectory);

describe('DeleteSessionJob', () => {
    afterEach(jest.restoreAllMocks);

    test('should call removeFileOrDirectory with sessionPath', async () => {
         jest.spyOn(fileUtils, 'removeFileOrDirectory').mockResolvedValue(undefined);
        const context = {operationId: 'fake'} as DeleteSessionJobContext;
        const job = new DeleteSessionJob();

        await job.execute(context);

        expect(mockedRemoveFileOrDirectory).toHaveBeenCalledWith('/path/to/session');
    });
});
