import {BinaryProxy, CheckSessionParams} from '../../../../dependencies/BinaryProxy';
import {CheckSessionJob, CheckSessionJobContext} from '../../../../runs/jobs/CheckSessionJob';
import {JobStoppedByUserError} from '../../../../runs/JobStoppedByUserError';
import {Command} from '../../../../types';

describe('CheckSessionJob', () => {
    afterEach(jest.restoreAllMocks);

    const checkSessionParams = {some: 'value'} as any as CheckSessionParams;

    const createMockBinaryProxy = (result: Promise<any>): jest.Mocked<BinaryProxy> => {
        return {run: jest.fn().mockResolvedValue({result})} as any;
    };

    test('execute checks the session and ends gracefully if it is ok', async () => {
        const context = {operationId: 'fake'} as CheckSessionJobContext;
        const checkSessionResult = {hasScrapeBlockingIssues: false, id: 'test'};
        const binaryProxyMock = createMockBinaryProxy(Promise.resolve(checkSessionResult));
        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);
        
        await job.execute(context);
        expect(context).toStrictEqual({operationId: 'fake', sessionValid: true, checkSessionResult});
        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.CHECK_SESSION, checkSessionParams, context.operationId);
    });

    test('execute checks the session but is killed before it ends', async () => {
        const context = {operationId: 'fake'} as CheckSessionJobContext;
        const binaryProxyMock = createMockBinaryProxy(Promise.reject(new Error('Session check failed')));
        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);
        await job.kill(context);
        expect(context).toStrictEqual({operationId: 'fake'});
        await expect(job.execute(context)).rejects.toThrow(new JobStoppedByUserError('CHECK_SESSION_EXECUTE_KILL'));
    });

    test('execute sets sessionValid to false in case of invalid session', async () => {
        const context = {operationId: 'fake'} as CheckSessionJobContext;
        const binaryProxyMock = createMockBinaryProxy(Promise.reject({hasScrapeBlockingIssues: true, id: 'test'}));

        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);
        await job.execute(context);
        expect(context).toStrictEqual({operationId: 'fake', sessionValid: false});
        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.CHECK_SESSION, checkSessionParams, context.operationId);
    });
});
