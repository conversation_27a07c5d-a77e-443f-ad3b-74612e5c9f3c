import {errorType} from '../../../../configurations/errorType';
import {BinaryProxy, CheckSessionParams} from '../../../../dependencies/BinaryProxy';
import {ScraperLibError} from '../../../../processes/types/errors';
import {CheckSessionJob, CheckSessionJobContext} from '../../../../runs/jobs/CheckSessionJob';
import {Command} from '../../../../types';

describe('CheckSessionJob', () => {
    afterEach(jest.restoreAllMocks);

    const checkSessionParams = {some: 'value'} as any as CheckSessionParams;
    const context = {operationId: 'fake'} as CheckSessionJobContext;

    test('execute checks the session and ends gracefully if it is ok', async () => {
        const checkSessionResult = {hasScrapeBlockingIssues: false, id: 'test'};
        const binaryProxyMock = {
            run: jest.fn().mockResolvedValue({
                result: Promise.resolve(checkSessionResult)
            })
        } as unknown as BinaryProxy;
        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);
        await job.execute(context);
        expect(context).toStrictEqual({...context, sessionValid: true, checkSessionResult});
        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.CHECK_SESSION, checkSessionParams, context.operationId);
    });

    test('execute checks the session but is killed before it ends', async () => {
        const binaryProxyMock = {
            run: jest.fn().mockResolvedValue({
                result: Promise.reject()
            })
        } as unknown as BinaryProxy;
        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);
        await job.kill(context);
        expect(context).toStrictEqual({...context, sessionValid: undefined, checkSessionResult: undefined});
        await expect(job.execute(context)).rejects.toThrow(new ScraperLibError(errorType.JOB_STOPPED_BY_USER));
    });

    test('execute sets sessionValid to false in case of invalid session', async () => {
        const binaryProxyMock = {
            run: jest.fn().mockResolvedValue({
                result: Promise.reject({hasScrapeBlockingIssues: true, id: 'test'})
            })
        } as unknown as BinaryProxy;

        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);
        await job.execute(context);
        expect(context).toStrictEqual({...context, sessionValid: false, checkSessionResult: undefined});
        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.CHECK_SESSION, checkSessionParams, context.operationId);
    });
});
