import {BinaryProxy, CheckSessionParams} from '../../../../dependencies/BinaryProxy';
import {CheckSessionJob, CheckSessionJobContext} from '../../../../runs/jobs/CheckSessionJob';
import {JobStoppedByUserError} from '../../../../runs/JobStoppedByUserError';
import {Command} from '../../../../types';
import {Deferred} from '../../../../utils/Deferred';

describe('CheckSessionJob', () => {
    afterEach(jest.restoreAllMocks);

    const checkSessionParams = {some: 'value'} as any as CheckSessionParams;

    const createMockBinaryProxy = (result: Promise<any>): jest.Mocked<BinaryProxy> => {
        return {run: jest.fn().mockResolvedValue({result})} as any;
    };

    test('execute checks the session and ends gracefully if it is ok', async () => {
        const context = {operationId: 'fake'} as CheckSessionJobContext;
        const checkSessionResult = {hasScrapeBlockingIssues: false, id: 'test'};
        const binaryProxyMock = createMockBinaryProxy(Promise.resolve(checkSessionResult));
        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);

        const result = await job.execute(context);

        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.CHECK_SESSION, checkSessionParams, context.operationId);
        expect(result).toBe(checkSessionResult);
        expect(context).toStrictEqual({operationId: 'fake', sessionValid: true, checkSessionResult});
    });

    test('execute checks the session but is killed before it ends', async () => {
        const context = {operationId: 'fake'} as CheckSessionJobContext;

        // Create a deferred promise that we can control
        const executionResult = new Deferred<any>();

        // Mock execution with kill method
        const mockExecution = {
            result: executionResult.promise,
            kill: jest.fn()
        };

        // Use Deferred to control when binaryProxy.run resolves
        const binaryProxyRunDeferred = new Deferred<typeof mockExecution>();

        const binaryProxyMock = {
            run: jest.fn().mockReturnValue(binaryProxyRunDeferred.promise)
        } as any;

        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);

        // Start execute (which will create this.execution after binaryProxy.run resolves)
        const executePromise = job.execute(context);

        // Resolve binaryProxy.run to set this.execution
        binaryProxyRunDeferred.resolve(mockExecution);

        // Wait for next tick to ensure this.execution is set
        await new Promise((resolve) => setTimeout(resolve, 0));

        // Kill the job while execute is running
        await job.kill(context);

        // Now reject the execution to simulate the session check failing
        executionResult.reject(new Error('Session check failed'));

        // Verify execution.kill was called
        expect(mockExecution.kill).toHaveBeenCalledWith(context.operationId);

        // Verify execute throws the correct error
        await expect(executePromise).rejects.toThrow(new JobStoppedByUserError('CHECK_SESSION_EXECUTE_KILL'));

        // Context should remain unchanged since job was killed
        expect(context).toStrictEqual({operationId: 'fake'});
    });

    test('execute sets sessionValid to false in case of invalid session', async () => {
        const context = {operationId: 'fake'} as CheckSessionJobContext;
        const binaryProxyMock = createMockBinaryProxy(Promise.reject({hasScrapeBlockingIssues: true, id: 'test'}));

        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);
        await job.execute(context);
        expect(context).toStrictEqual({operationId: 'fake', sessionValid: false});
        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.CHECK_SESSION, checkSessionParams, context.operationId);
    });
});
