import {SaveCookiesToSessionJob} from '../../../../runs/jobs/SaveCookiesToSessionJob';
import {EncryptedJsonFile} from '../../../../utils/EncryptedJsonFile';
import * as fileUtils from '../../../../utils/fileUtils';
import {createMockLoginWithCookiesRunContext} from './loadCookies.helper';

describe('LoadCookiesToSessionJob', () => {
    afterEach(() => jest.resetAllMocks);

    it('should save cookies to session file when executed', async () => {
        // Given
        const encryptedJsonSaveSpy = jest.spyOn(EncryptedJsonFile.prototype, 'save').mockResolvedValue(undefined);
        const job = new SaveCookiesToSessionJob();
        const context = createMockLoginWithCookiesRunContext({
            sessionPath: 'custom/session/path',
            cookies: ['custom-cookie1', 'custom-cookie2']
        });

        // When
        await job.execute(context);

        // Then
        expect(encryptedJsonSaveSpy).toHaveBeenCalledWith({cookies: ['custom-cookie1', 'custom-cookie2']});
    });

    it('should remove session file when killed with throwOnFail=false', async () => {
        // Given
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory').mockResolvedValue(undefined);
        const job = new SaveCookiesToSessionJob();
        const context = createMockLoginWithCookiesRunContext({
            sessionPath: 'path/to/session/file.json'
        });

        // When
        await job.kill(context);

        // Then
        expect(removeFileOrDirectorySpy).toHaveBeenCalledWith('path/to/session/file.json', false);
    });

    it('should remove session file when job fails with throwOnFail=false', async () => {
        // Given
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory').mockResolvedValue(undefined);
        const job = new SaveCookiesToSessionJob();
        const error = new Error('Test error');
        const context = createMockLoginWithCookiesRunContext({
            sessionPath: 'path/to/failed/session.json'
        });

        // When
        await job.onFail(error, context);

        // Then
        expect(removeFileOrDirectorySpy).toHaveBeenCalledWith('path/to/failed/session.json', false);
    });

    it('should complete onSuccess without error', async () => {
        // Given
        const job = new SaveCookiesToSessionJob();
        const context = createMockLoginWithCookiesRunContext();

        // When & Then
        await expect(job.onSuccess(context)).resolves.toBeUndefined();
    });

    it('should propagate error when saving cookies fails', async () => {
        // Given
        const error = new Error('Save failed');
        const encryptedJsonSaveSpy = jest.spyOn(EncryptedJsonFile.prototype, 'save').mockRejectedValue(error);
        const job = new SaveCookiesToSessionJob();
        const context = createMockLoginWithCookiesRunContext({
            cookies: ['test-cookie']
        });

        // When & Then
        await expect(job.execute(context)).rejects.toThrow('Save failed');
        expect(encryptedJsonSaveSpy).toHaveBeenCalledWith({cookies: ['test-cookie']});
    });

    it('should not throw when removeFileOrDirectory fails in kill due to throwOnFail=false', async () => {
        // Given
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory').mockImplementation(async (_path, throwOnFail) => {
            // Simulate the real behavior: when throwOnFail=false, errors are caught and not thrown
            if (throwOnFail === false) {
                //TODO duplication
                return Promise.resolve(); // Don't throw, just resolve
            }
            throw new Error('File removal failed');
        });
        const job = new SaveCookiesToSessionJob();
        const context = createMockLoginWithCookiesRunContext({
            sessionPath: 'problematic/path.json'
        });

        // When & Then
        // Should not throw because throwOnFail=false is passed to removeFileOrDirectory
        await expect(job.kill(context)).resolves.toBeUndefined();
        expect(removeFileOrDirectorySpy).toHaveBeenCalledWith('problematic/path.json', false);
    });

    it('should not throw when removeFileOrDirectory fails in onFail due to throwOnFail=false', async () => {
        // Given
        const originalError = new Error('Original job error');
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory').mockImplementation(async (_path, throwOnFail) => {
            // Simulate the real behavior: when throwOnFail=false, errors are caught and not thrown
            if (throwOnFail === false) {
                return Promise.resolve(); // Don't throw, just resolve
            }
            throw new Error('File removal failed');
        });
        const job = new SaveCookiesToSessionJob();
        const context = createMockLoginWithCookiesRunContext({
            sessionPath: 'problematic/path.json'
        });

        // When & Then
        // Should not throw because throwOnFail=false is passed to removeFileOrDirectory
        await expect(job.onFail(originalError, context)).resolves.toBeUndefined();
        expect(removeFileOrDirectorySpy).toHaveBeenCalledWith('problematic/path.json', false);
    });
});
