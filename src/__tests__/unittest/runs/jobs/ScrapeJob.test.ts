import {BinaryProxy, ScrapeParams} from '../../../../dependencies/BinaryProxy';
import {ScrapeResult} from '../../../../processes/types';
import {Execution} from '../../../../processes/types/Execution';
import {ScrapeRunContext} from '../../../../runs/jobs/context';
import {ScrapeJob} from '../../../../runs/jobs/ScrapeJob';
import {Source} from '../../../../types';
import {sumDaysInDateRanges} from '../../../../utils/datesUtils';
import {createDateRange} from '../../../utils/datesHelpers';
import {asyncVoidFunc} from '../../../utils/helpers';

function getBinaryProxyMock(scrapeResult: ScrapeResult) {
    const killMock = jest.fn().mockResolvedValue(undefined);
    return {
        run: jest.fn().mockResolvedValue({
            kill: killMock as unknown as (operationId: string) => Promise<void>,
            result: Promise.resolve([scrapeResult] as unknown as ScrapeResult[])
        } as Execution<ScrapeResult[]>),
        killMock
    } as unknown as BinaryProxy & {killMock: jest.Mock<Promise<void>, []>};
}

describe('ScrapeJob', () => {
    test('ScrapeJob updates context.latestScrapeResults in case of successful scrape', async () => {
        const scrapeResult = {dateFrom: '2020-01-01', dateTo: '2020-01-07'} as unknown as ScrapeResult;
        const scrapeJob = new ScrapeJob(getBinaryProxyMock(scrapeResult), {} as ScrapeParams, asyncVoidFunc, asyncVoidFunc);

        const context = {
            latestScrapeResults: []
        } as unknown as ScrapeRunContext;
        await scrapeJob.execute(context);
        expect(context.latestScrapeResults).toEqual([scrapeResult]);
    });

    describe('ScrapeJob.onSuccess', () => {
        const scrapeResult = {} as unknown as ScrapeResult;
        const updateStatus = jest.fn();
        const updateLastScrapeDate = jest.fn();
        const scrapeJob = new ScrapeJob(getBinaryProxyMock(scrapeResult), {source: Source.PLAYSTATION_SALES} as ScrapeParams, updateStatus, updateLastScrapeDate);
        let context: ScrapeRunContext;
        const dateRange1 = createDateRange('2020-01-01', '2020-01-04');
        const dateRange2 = createDateRange('2020-01-08', '2020-01-15');

        beforeEach(async () => {
            context = {
                numberOfDaysToScrape: sumDaysInDateRanges([dateRange1, dateRange2]),
                dateRangesToProcess: [dateRange1, dateRange2]
            } as ScrapeRunContext;
            await scrapeJob.onSuccess(context);
        });

        afterEach(jest.resetAllMocks);

        //TODO WTF?
        //TODO add event sending tests
        test('ScrapeJob.onSuccess calls updateLastScrapeDate in case of success', async () => {
            expect(updateLastScrapeDate).toHaveBeenCalledWith(Source.PLAYSTATION_SALES);
        });

        test('ScrapeJob.onSuccess calls updateStatus with RUNNING_SCRAPE status, scrapedDays and numberOfDaysToScrape in case of success', async () => {
            expect(updateStatus).toHaveBeenCalledWith('RUNNING_SCRAPE', {scrapedDays: 8, numberOfDaysToScrape: context.numberOfDaysToScrape});
        });

        test('ScrapeJob.onSuccess pops dateRangesToProcess in case of success', async () => {
            expect(context.dateRangesToProcess).toEqual([dateRange1]);
        });
    });

    test('ScrapeJob.kill calls kill on the execution', async () => {
        const binaryProxy = getBinaryProxyMock({} as ScrapeResult);
        const scrapeJob = new ScrapeJob(binaryProxy, {} as ScrapeParams, asyncVoidFunc, asyncVoidFunc);
        const context = {} as ScrapeRunContext;
        await scrapeJob.execute(context);

        await scrapeJob.kill(context);
        expect(binaryProxy.killMock).toHaveBeenCalled();
    });
});
