import {errorType} from '../../../configurations/errorType';
import {EphemeralScraperConfigStates, ScraperConfiguration, ScraperConfigurationStatus} from '../../../configurations/ScraperConfiguration';
import {ScraperLibEmitterObject} from '../../../emitter';
import {UpdateLastScrapeDate, UpdateStatus, getStatusHelpers} from '../../../runs/status';
import {ScraperLib} from '../../../ScraperLib';
import {ElectronStorageImplementation} from '../../../storage/ElectronStorageImplementation';
import {Storage} from '../../../storage/Storage';
import {Source, eventTypes} from '../../../types';
import {getFreshLib, newAdapterWithStorage, testSourceAccountWithParams} from '../../utils/helpers';

describe('status helpers', () => {
    let updateStatus: UpdateStatus;
    let updateLastScrapeDate: UpdateLastScrapeDate;
    let scraperLib: ScraperLib;
    let events: any[];
    let storage: Storage;
    let editScraperConfiguration: jest.SpyInstance<Promise<ScraperConfiguration>>;

    beforeEach(async () => {
        const {adapter} = newAdapterWithStorage();
        storage = new ElectronStorageImplementation(adapter);
        editScraperConfiguration = jest.spyOn(storage, 'editScraperConfiguration');

        events = [];
        scraperLib = await getFreshLib({
            emitter: ({message}: ScraperLibEmitterObject) => events.push(JSON.parse(message)),
            storageAdapter: adapter,
            withMockPriorityQueue: true
        });

        const scraperConfig = await scraperLib.addScraperConfiguration({source: Source.PLAYSTATION_SALES}, testSourceAccountWithParams());
        ({updateStatus, updateLastScrapeDate} = getStatusHelpers(storage, scraperConfig));
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('updateStatus', () => {
        const additionalParams = {scrapedDays: 5, numberOfDaysToScrape: 10, progress: 0.5};

        Object.values(ScraperConfigurationStatus).forEach((newStatus) => {
            test(`emits an ${eventTypes.scraperRunUpdate} event for ${newStatus} status and updates configuration if it is non ephemeral`, async () => {
                await updateStatus(newStatus, additionalParams);

                expect(events).toContainEqual({
                    source: Source.PLAYSTATION_SALES,
                    newStatus,
                    type: eventTypes.scraperRunUpdate,
                    ...additionalParams
                });
                const shouldBeUpdated = !EphemeralScraperConfigStates.includes(newStatus);
                expect(editScraperConfiguration).toHaveBeenCalledTimes(shouldBeUpdated ? 1 : 0);
            });
        });

        test('converts INCORRECT_CREDENTIALS error to PENDING_PERMISSIONS status for GOOGLE_SALES only if 48 hours not passed yet since config creation', async () => {
            const expectedEventBefore48h = {
                source: Source.GOOGLE_SALES,
                newStatus: ScraperConfigurationStatus.PENDING_PERMISSIONS,
                type: eventTypes.scraperRunUpdate
            };
            const expectedEventAfter48h = {
                source: Source.GOOGLE_SALES,
                newStatus: ScraperConfigurationStatus.ERROR,
                errorType: errorType.INCORRECT_CREDENTIALS,
                type: eventTypes.scraperRunUpdate
            };
            const scraperConfig = await scraperLib.addScraperConfiguration({source: Source.GOOGLE_SALES}, testSourceAccountWithParams());

            await Promise.all(
                [
                    {msAfterCreation: 0, expectedEvent: expectedEventBefore48h},
                    {msAfterCreation: 48 * 60 * 60 * 1000 - 10, expectedEvent: expectedEventBefore48h},
                    {msAfterCreation: 48 * 60 * 60 * 1000, expectedEvent: expectedEventAfter48h},
                    {msAfterCreation: 49 * 60 * 60 * 1000, expectedEvent: expectedEventAfter48h}
                ].map(async ({msAfterCreation, expectedEvent}) => {
                    events.length = 0;

                    const newConfig = await storage.editScraperConfiguration({...scraperConfig, createdAt: new Date(Date.now() - msAfterCreation)});
                    await getStatusHelpers(storage, newConfig).updateStatus(ScraperConfigurationStatus.ERROR, {errorType: errorType.INCORRECT_CREDENTIALS});

                    expect(events).toContainEqual(expectedEvent);
                })
            );
        });
    });

    describe('updateLastScrapeDate', () => {
        test('edits scraper configuration to update lastSuccessfulScrapeDate', async () => {
            const config = await storage.getScraperConfigurationBySource(Source.PLAYSTATION_SALES);
            const lastSuccessfulScrapeDate = new Date();
            jest.useFakeTimers();
            jest.setSystemTime(lastSuccessfulScrapeDate);

            expect(config!.lastSuccessfulScrapeDate).toBeUndefined();
            await updateLastScrapeDate(Source.PLAYSTATION_SALES);

            const updatedConfig = await storage.getScraperConfigurationBySource(Source.PLAYSTATION_SALES);
            expect(updatedConfig!.lastSuccessfulScrapeDate).toEqual(lastSuccessfulScrapeDate);

            jest.useRealTimers();
        });
    });
});
