import {ScraperLib} from '../../../ScraperLib';
import {getFreshLib} from '../../utils/helpers';

let scrapeAllSourcesSpy: jest.SpyInstance;

describe('Running scrapes on missed scheduled runs', () => {
    beforeEach(() => {
        scrapeAllSourcesSpy = jest.spyOn(ScraperLib.prototype, 'scrapeAllSources').mockImplementation(() => Promise.resolve([]));
    });

    afterEach(() => {
        jest.runOnlyPendingTimers();
        jest.useRealTimers();
        jest.resetAllMocks();
    });

    test.each([
        {currentFakeTime: '00:00:00', schedule: {hours: 12, minutes: 0}, expectedCallsNumber: 0},
        {currentFakeTime: '09:15:00', schedule: {hours: 12, minutes: 0}, expectedCallsNumber: 0},
        {currentFakeTime: '11:59:59.999', schedule: {hours: 12, minutes: 0}, expectedCallsNumber: 0},
        {currentFakeTime: '12:00:00.001', schedule: {hours: 12, minutes: 0}, expectedCallsNumber: 1},
        {currentFakeTime: '23:59:59.999', schedule: {hours: 12, minutes: 0}, expectedCallsNumber: 1},
        {currentFakeTime: '14:00:00', schedule: {hours: 13, minutes: 30}, expectedCallsNumber: 1}
    ])(
        'when run at $currentFakeTime, with schedule set to $schedule, scrapers should be called $expectedCallsNumber time(s)',
        async ({currentFakeTime, schedule, expectedCallsNumber}) => {
            jest.useFakeTimers().setSystemTime(new Date(`2020-01-01 ${currentFakeTime}`));
            const scraperLib = await getFreshLib({skipInit: false});
            await scraperLib.setupDailyCron([schedule]);
            await scraperLib.init({cron: {start: true, forceMissedScheduledRuns: true}});
            expect(scrapeAllSourcesSpy).toBeCalledTimes(expectedCallsNumber);
        }
    );
});
