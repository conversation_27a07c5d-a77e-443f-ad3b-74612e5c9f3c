import {validateSchedule} from '../../../cron/paramsValidation';

describe('validateSchedule', () => {
    it('should properly validate hours as number', () => {
        expect(validateSchedule({hours: 12, minutes: 0})).toEqual({hours: 12, minutes: 0});
    });

    it('should throw error when hours is integer out of range', () => {
        expect(() => validateSchedule({hours: 50, minutes: 0})).toThrow('Value is out of range');
    });

    it('should throw error when hours is negative', () => {
        expect(() => validateSchedule({hours: -1, minutes: 0})).toThrow('Value is out of range');
    });

    it('should throw error when hours is not integer', () => {
        expect(() => validateSchedule({hours: 12.5, minutes: 0})).toThrow('Value should be integer number');
    });

    it('should properly validate minutes as number', () => {
        expect(validateSchedule({hours: 12, minutes: 30})).toEqual({hours: 12, minutes: 30});
    });

    it('should throw error when minutes is integer out of range', () => {
        expect(() => validateSchedule({hours: 12, minutes: 60})).toThrow('Value is out of range');
    });

    it('should throw error when minutes is negative', () => {
        expect(() => validateSchedule({hours: 12, minutes: -1})).toThrow('Value is out of range');
    });
});
