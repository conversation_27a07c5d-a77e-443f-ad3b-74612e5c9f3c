import {promiseState} from '../../../utils/asyncUtils';

describe('promiseState', () => {
    [
        {promise: new Promise((resolve) => resolve('')), state: 'fulfilled'},
        {promise: new Promise((_, reject) => reject(new Error())), state: 'rejected'},
        {promise: new Promise(() => undefined), state: 'pending'}
    ].forEach(({promise, state}) => {
        test(`Promise should be ${state}`, async () => {
            expect(await promiseState(promise)).toBe(state);
        });
    });
});
