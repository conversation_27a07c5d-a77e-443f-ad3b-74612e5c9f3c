import {ErrorType} from '../../../configurations/errorType';
import {ScraperLibError} from '../../../processes/types/errors';
import {deepStringify} from '../../../utils/deepStringify';

describe('deepStringify', () => {
    it('should stringify error', () => {
        const error = new Error('This is a test');
        error.stack = undefined; // ignore stack to make test deterministic
        const result = deepStringify(error);
        expect(result).toBe('{"name":"Error","message":"This is a test"}');
    });

    it('should stringify custom error', () => {
        const error = new ScraperLibError('CUSTOM_TYPE' as ErrorType, 'info');
        error.stack = undefined; // ignore stack to make test deterministic
        const result = deepStringify(error);
        expect(result).toBe('{"name":"Error","message":"CUSTOM_TYPE","errorType":"CUSTOM_TYPE","logLevel":"info","errorCode":"SCL_UNSPECIFIED"}');
    });

    it('should stringify object with circular reference', () => {
        const obj = {a: 1};
        obj['b'] = obj;
        const result = deepStringify(obj);
        expect(result).toBe('{"a":1,"b":"[Circular ~]"}');
    });
});
