import {promises as fs} from 'fs';
import * as mockFs from 'mock-fs';
import {copyFile, moveFile, removeFileOrDirectory} from '../../../utils/fileUtils';

describe('fileUtils', () => {
    beforeEach(() => {
        // for some reason, mockFs starts with /tmp and /home, so we operate inside /dir
        mockFs({'/dir/foo': ''});
    });

    afterEach(() => {
        mockFs.restore();
        jest.restoreAllMocks();
    });

    describe('moveFile', () => {
        test('moveFile should be able to do a simple rename ', async () => {
            await moveFile('/dir/foo', '/dir/bar');
            const result = await fs.readdir(`/dir`);
            expect(result).toEqual(['bar']);
        });

        test('moveFile should be able to move a file to a directory of the same name ', async () => {
            await moveFile('/dir/foo', '/dir/foo/bar');

            expect(await fs.readdir(`/dir/`)).toEqual(['foo']);
            expect((await fs.lstat('/dir/foo')).isDirectory()).toBe(true);
            expect(await fs.readdir(`/dir/foo`)).toEqual(['bar']);
            expect((await fs.lstat('/dir/foo/bar')).isFile()).toBe(true);
        });
    });

    test('Trying to remove a non existing directory should not throw error', async () => {
        await removeFileOrDirectory('someDirectoryThatDoesNotExist');
        expect(1).toEqual(1);
    });

    test('Trying to remove a non existing file should not throw error if not confured to', async () => {
        const fsSpy = jest.spyOn(fs, 'rm');
        fsSpy.mockImplementation(() => {
            throw new Error('error, different than file not found');
        });

        await removeFileOrDirectory('/dir/foo', false);
        expect(fsSpy).toHaveBeenCalledTimes(1);
    });

    test('Trying to remove a non existing file should throw error if configured to', async () => {
        const fsSpy = jest.spyOn(fs, 'rm');
        const message = 'error, different than file not found';
        fsSpy.mockImplementation(() => {
            throw new Error(message);
        });

        await expect(removeFileOrDirectory('/dir/foo', true)).rejects.toThrow(message);
    });

    test('copyFile', async () => {
        mockFs({'/dir/foo': 'content'});
        await copyFile('/dir/foo', '/dir/bar/baz');
        expect(await fs.readFile('/dir/bar/baz', 'utf8')).toEqual('content');
    });
});
