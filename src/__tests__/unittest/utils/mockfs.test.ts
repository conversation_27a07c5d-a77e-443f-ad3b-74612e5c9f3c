import * as fs from 'fs';
import * as path from 'path';
import * as mockFs from 'mock-fs';

describe('Tests making sure, mockFs works as expected, because with some node versions is causes problems', () => {
    afterEach(() => {
        mockFs.restore();
    });

    it('should check file existence with different methods', async () => {
        const filePath = '/some/random/path/to/file.txt';
        mockFs({[filePath]: 'dummy file content'});

        expect(fs.existsSync(filePath)).toBeTruthy();
        expect(await checkFileExistsWithPromises(filePath)).toBeTruthy();
        expect(checkFileExistsWithStats(filePath)).toBeTruthy();
        expect(checkFileExistsWithReaddir(filePath)).toBeTruthy();
        expect(tryReadFile(filePath)).toEqual('dummy file content');
    });
});

async function checkFileExistsWithPromises(filePath: string): Promise<boolean> {
    try {
        await fs.promises.access(filePath);
        return true;
    } catch {
        return false;
    }
}

function checkFileExistsWithStats(filePath: string): boolean {
    try {
        return fs.statSync(filePath).isFile();
    } catch {
        return false;
    }
}

function checkFileExistsWithReaddir(filePath: string): boolean {
    try {
        const dirPath = path.dirname(filePath);
        const fileName = path.basename(filePath);
        return fs.readdirSync(dirPath).includes(fileName);
    } catch {
        return false;
    }
}

function tryReadFile(filePath: string): string | null {
    try {
        return fs.readFileSync(filePath, 'utf8');
    } catch {
        return null;
    }
}
