import {ScraperLibError} from '../../../processes/types/errors';
import {ensureJsonSerializable} from '../../../utils/serialization';

describe('ensureJsonSerializable', () => {
    test('should throw ScraperLibError with correct error type and message for non-serializable objects', () => {
        const circularObject: any = {name: 'test'};
        circularObject.self = circularObject;
        const errorMessage = 'Custom error message for circular reference';

        expect(() => ensureJsonSerializable(circularObject, errorMessage)).toThrow(ScraperLibError);
    });

    test('should return the original object if it is serializable', () => {
        const serializableObject = {name: 'test '};
        const result = ensureJsonSerializable(serializableObject, 'Custom error message');
        expect(result).toBe(serializableObject);
    });

    test('should return non modified object if it is serializable', () => {
        const serializableObject = {name: 'test '};
        const clone = {...serializableObject};

        const result = ensureJsonSerializable(serializableObject, 'Custom error message');
        expect(result).toStrictEqual(clone);
    });
});
