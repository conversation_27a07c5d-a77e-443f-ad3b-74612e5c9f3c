import {AxiosError} from 'axios';
import * as pauseUtil from '../../../utils/pause';
import {retryAction} from '../../../utils/retryAction';

describe('retryAction', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });

    test('should properly rethrow error when reached maxRetries', async () => {
        const expectedError = new Error('error');
        const fun = retryAction({
            target: async () => {
                throw expectedError;
            },
            maxAttempts: 0
        });
        await expect(fun).rejects.toThrow(expectedError);
    });

    test('should properly repeat until maxRetries reached', async () => {
        const target = jest.fn(async () => {
            throw new Error('error');
        });
        const fun = retryAction({target, maxAttempts: 2});

        await expect(fun).rejects.toThrow();

        expect(target).toHaveBeenCalledTimes(2);
    });

    test('should stop immediately once retryCondition returns false', async () => {
        const target = jest.fn(async () => {
            throw new Error('error');
        });

        const retryCondition = jest.fn();
        retryCondition.mockReturnValueOnce(true);
        retryCondition.mockReturnValueOnce(false);

        const expectedAttempts = 2;
        const fun = retryAction({target, maxAttempts: 5, retryCondition});
        await expect(fun).rejects.toThrow();
        expect(target).toHaveBeenCalledTimes(expectedAttempts);
        expect(retryCondition).toHaveBeenCalledTimes(expectedAttempts);
    });

    test('should delay between attempts without backoff', async () => {
        const pauseSpy = jest.spyOn(pauseUtil, 'pause');
        const target = jest.fn(async () => {
            throw new Error('error');
        });
        const delay = 100;
        const fun = retryAction({target, maxAttempts: 3, delay});
        await expect(fun).rejects.toThrow();
        expect(target).toHaveBeenCalledTimes(3);
        expect(pauseSpy).toHaveBeenCalledTimes(2);
        expect(pauseSpy).toHaveBeenNthCalledWith(1, delay);
        expect(pauseSpy).toHaveBeenNthCalledWith(2, delay);
    });

    test('should delay between attempts with backoff', async () => {
        const pauseSpy = jest.spyOn(pauseUtil, 'pause');
        const target = jest.fn(async () => {
            throw new Error('error');
        });
        const delay = 100;
        const fun = retryAction({target, maxAttempts: 4, delay, backoff: 2});
        await expect(fun).rejects.toThrow();
        expect(target).toHaveBeenCalledTimes(4);
        expect(pauseSpy).toHaveBeenCalledTimes(3);
        expect(pauseSpy).toHaveBeenNthCalledWith(1, 200);
        expect(pauseSpy).toHaveBeenNthCalledWith(2, 400);
        expect(pauseSpy).toHaveBeenNthCalledWith(3, 800);
    });

    test('should not log sensitive data from AxiosError like Authorization header before second attempt', async () => {
        const logger = {
            info: jest.fn(),
            error: jest.fn()
        };
        const target = jest.fn(async () => {
            throw new AxiosError('SpecificErrorMessage', '500', {headers: {Authorization: 'Bearer token'}}, null);
        });

        const fun = retryAction({target, maxAttempts: 2, logger});
        await expect(fun).rejects.toThrow();
        expect(logger.error).toHaveBeenCalledTimes(1);

        const firstCallParams = JSON.parse(logger.error.mock.calls[0][0]);
        expect(firstCallParams).toHaveProperty('message', 'SpecificErrorMessage');
        expect(firstCallParams).toHaveProperty('config', {headers: {}});
        expect(firstCallParams).toHaveProperty('code', '500');
    });

    test('should properly retry in case of error with circular structure', async () => {
        const errorWithCircularReference: any = new Error('error');
        errorWithCircularReference.circular = errorWithCircularReference;

        const target = jest.fn(async () => {
            throw errorWithCircularReference;
        });
        const fun = retryAction({target, maxAttempts: 2});
        await expect(fun).rejects.toThrow();
        expect(target).toHaveBeenCalledTimes(2);
    });
});
