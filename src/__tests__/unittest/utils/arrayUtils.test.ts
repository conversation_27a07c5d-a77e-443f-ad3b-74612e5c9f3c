import {filterItemsFromArray} from '../../../utils/arrayUtils';

describe('filterItemsFromArray', () => {
    // simple types
    [
        {description: 'should return empty array from empty array', sourceItems: [], itemsToFilter: [], expected: [], condition: () => false},
        {
            description: 'should return an empty array if all items are filtered out',
            sourceItems: [1, 2, 3],
            itemsToFilter: [1, 2, 3],
            expected: [],
            condition: (x, y) => x === y
        },
        {
            description: 'should original array if no items fulfill condition',
            sourceItems: [1, 2, 3],
            itemsToFilter: [4, 5, 6],
            expected: [1, 2, 3],
            condition: (x, y) => x === y
        },
        {description: 'should correctly filter items', sourceItems: [1, 2, 3], itemsToFilter: [1], expected: [2, 3], condition: (x, y) => x === y},
        {description: 'should correctly filter items', sourceItems: [1, 2, 3], itemsToFilter: [2], expected: [1, 3], condition: (x, y) => x === y},
        {description: 'should filter all items (condition)', sourceItems: [1, 2, 3], itemsToFilter: [1], expected: [1, 2, 3], condition: () => false},
        {description: 'should not filter any items (condition)', sourceItems: [1, 2, 3], itemsToFilter: [1], expected: [], condition: () => true},
        {description: 'should filter duplicated items if condition is correct', sourceItems: [1, 1, 1], itemsToFilter: [1], expected: [], condition: (x, y) => x === y}
    ].forEach(({description, sourceItems, itemsToFilter, condition, expected}) => {
        test(description, async () => {
            expect(filterItemsFromArray(sourceItems, itemsToFilter, condition)).toEqual(expected);
        });
    });

    // complex types (typescript don't allow to mix them in one array)
    [
        {
            description: 'should correctly filter items',
            sourceItems: [
                {x: 1, y: 1, z: 1},
                {x: 2, y: 2, z: 2},
                {x: 3, y: 3, z: 3}
            ],
            itemsToFilter: [{x: 2, y: 2, z: 3}],
            expected: [
                {x: 1, y: 1, z: 1},
                {x: 3, y: 3, z: 3}
            ],
            condition: (obj1, obj2) => obj1.x === obj2.x && obj1.y === obj2.y
        },
        {
            description: 'should correctly filter items when source items are more complex than items to filter',
            sourceItems: [
                {x: 1, y: 1, z: 1},
                {x: 2, y: 2, z: 2},
                {x: 3, y: 3, z: 3}
            ],
            itemsToFilter: [{x: 2}],
            expected: [
                {x: 1, y: 1, z: 1},
                {x: 3, y: 3, z: 3}
            ],
            condition: (obj1, obj2) => obj1.x === obj2.x
        }
    ].forEach(({description, sourceItems, itemsToFilter, condition, expected}) => {
        test(description, async () => {
            expect(filterItemsFromArray(sourceItems, itemsToFilter, condition)).toEqual(expected);
        });
    });
});
