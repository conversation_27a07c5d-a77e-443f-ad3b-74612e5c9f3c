import {Source} from '../../../types';
import * as fileUtils from '../../../utils/fileUtils';
import {buildSessionPath, copySessionFileForShadowRun} from '../../../utils/session';
import * as session from '../../../utils/session';

describe('session utils', () => {
    afterEach(jest.restoreAllMocks);

    test('buildSessionPath', () => {
        const id = 'c5264f87-6650-4145-a6ab-a2bae53f09d0';
        jest.spyOn(session, 'createUUID').mockReturnValue(id);
        const expectedSessionPath = `/foo/bar/sessions/playstation_sales_${id}_session.json`;
        const sessionPath = buildSessionPath('/foo/bar', Source.PLAYSTATION_SALES);
        expect(sessionPath).toEqual(expectedSessionPath);
    });

    test('copySessionFileForShadowRun ', async () => {
        const copyFileSpy = jest.spyOn(fileUtils, 'copyFile').mockResolvedValue();
        jest.spyOn(session, 'createUUID').mockReturnValue('fb2385f4-011a-425c-ae71-2dc76888b4b2');
        const sessionPath = '/foo/bar/playstation_sales_c5264f87-6650-4145-a6ab-a2bae53f09d0_session.json';
        const expectedShadowRunSessionPath = '/foo/bar/playstation_sales_c5264f87-6650-4145-a6ab-a2bae53f09d0_session_shadow_run_fb2385f4.json';
        const shadowRunSessionPath = await copySessionFileForShadowRun(sessionPath);
        expect(copyFileSpy).toHaveBeenCalledWith(sessionPath, expectedShadowRunSessionPath);
        expect(shadowRunSessionPath).toEqual(expectedShadowRunSessionPath);
    });
});
