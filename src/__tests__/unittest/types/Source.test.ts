import * as featureFlags from '../../../featureFlags/featureFlags';
import {Source, filterSourcesByPrefix, steamGamesSources, steamPoweredSources} from '../../../types';
import {getRelatedSources} from '../../../utils/sourceUtils';

const relatedSourcesTestCases: Partial<Record<Source, Source[]>> = {
    [Source.NINTENDO_SALES]: [Source.NINTENDO_SALES, Source.NINTENDO_WISHLISTS, Source.NINTENDO_DISCOUNTS],
    [Source.STEAM_SALES]: [Source.STEAM_SALES, Source.STEAM_WISHLISTS],
    [Source.STEAM_IMPRESSIONS]: [Source.STEAM_IMPRESSIONS],
    [Source.META_QUEST_SALES]: [Source.META_RIFT_SALES, Source.META_QUEST_SALES]
};

describe('getRelatedSources', () => {
    let featureFlagsSpy: jest.SpyInstance;

    beforeEach(() => {
        featureFlagsSpy = jest.spyOn(featureFlags, 'isFeatureEnabled');
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    test.each(Object.keys(relatedSourcesTestCases))('should return correct related sources for %s', (source: Source) => {
        expect(getRelatedSources(source)).toStrictEqual(relatedSourcesTestCases[source]);
    });

    it('should return all related sources for nintendo if feature flags are enabled', () => {
        featureFlagsSpy.mockImplementation(() => true);
        expect(getRelatedSources(Source.NINTENDO_SALES)).toStrictEqual(filterSourcesByPrefix('nintendo_'));
    });

    it('should return all related sources for steam games if feature flags are enabled', () => {
        featureFlagsSpy.mockImplementation(() => true);
        expect(getRelatedSources(Source.STEAM_IMPRESSIONS)).toStrictEqual(steamGamesSources);
    });

    it('should return all related sources for steam powered if feature flags are enabled', () => {
        featureFlagsSpy.mockImplementation(() => true);
        expect(getRelatedSources(Source.STEAM_SALES)).toStrictEqual(steamPoweredSources);
    });
});
