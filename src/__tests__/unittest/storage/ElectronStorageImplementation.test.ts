import {existsSync} from 'fs';
import * as mockFs from 'mock-fs';
import {EphemeralScraperConfigStates, ScraperConfigurationStatus} from '../../../configurations/ScraperConfiguration';
import {ElectronStorageImplementation} from '../../../storage/ElectronStorageImplementation';
import {Source} from '../../../types/Source';
import {newAdapterWithStorage, testSourceAccountWithParams} from '../../utils/helpers';

describe('ElectronStorageImplementation', () => {
    const password = 'fizbaz';
    let storage: ElectronStorageImplementation;
    let memory: any;

    beforeEach(() => {
        mockFs({
            [testSourceAccountWithParams().sessionPath]: ''
        });
        const testStorage = newAdapterWithStorage();
        storage = new ElectronStorageImplementation(testStorage.adapter);
        memory = testStorage.memory;
    });

    afterEach(() => {
        mockFs.restore();
        jest.restoreAllMocks();
    });

    describe('Source Account', () => {
        test('should add new source account', async () => {
            const {id} = await storage.addSourceAccount(testSourceAccountWithParams({password}));

            const savedSourceAccount = await storage.getSourceAccount(id);

            expect(savedSourceAccount).toStrictEqual({id, ...testSourceAccountWithParams({password})});
        });

        test('should list source accounts', async () => {
            const sourceAccount1 = await storage.addSourceAccount(testSourceAccountWithParams({password}));
            const sourceAccount2 = await storage.addSourceAccount(testSourceAccountWithParams({password}));
            const list = await storage.listSourceAccounts();

            expect(list).toEqual([sourceAccount1, sourceAccount2]);
        });

        test('should throw if source account with provided id does not exist', async () => {
            const id = 'non-existing-id';
            await expect(storage.editSourceAccount({id, accountIdentifier: 'nonExisting'})).rejects.toThrow(
                `Unable to edit SourceAccount with id ${id}, because account was not found`
            );
        });

        test('should throw when tried to change userIdentifier', async () => {
            const {id} = await storage.addSourceAccount(testSourceAccountWithParams({password}));
            await expect(storage.editSourceAccount({id, accountIdentifier: 'fakeId'})).rejects.toThrow('Changing user identifier is not permitted');
        });

        test('should edit source account when tried to change to new userIdentifier for Microsoft', async () => {
            const {id} = await storage.addSourceAccount(
                testSourceAccountWithParams({
                    apiSetupData: [
                        {
                            companyName: 'test 1',
                            index: 0,
                            shouldDownload: false
                        },
                        {
                            companyName: 'test 2',
                            index: 1,
                            shouldDownload: false
                        },
                        {
                            companyName: 'test 3',
                            index: 2,
                            shouldDownload: true,
                            clientId: 'test 3',
                            clientSecret: 'test 3',
                            tenantId: 'test 3'
                        }
                    ]
                })
            );
            const newSourceAccount = {
                id,
                accountIdentifier: 'new test 2',
                cliParams: {
                    apiSetupData: [
                        {
                            companyName: 'test 2',
                            clientId: 'new test 2',
                            clientSecret: 'test 2',
                            tenantId: 'test 2'
                        }
                    ]
                }
            };
            const editedSourceAccount = await storage.editSourceAccount(newSourceAccount);
            expect(editedSourceAccount).toEqual({
                ...newSourceAccount,
                sessionPath: '.private/sessions/file.json'
            });
        });

        test('should edit source account', async () => {
            const {id, accountIdentifier} = await storage.addSourceAccount(testSourceAccountWithParams({password}));
            expect(accountIdentifier).toBeDefined();
            const editedSourceAccount = await storage.editSourceAccount({id, cliParams: {password: 'newPassword'}, accountIdentifier});
            expect(editedSourceAccount).toEqual({id, ...testSourceAccountWithParams({password: 'newPassword'})});
        });

        test('should remove old session file when updating with a new one', async () => {
            const existingSourceAccount = await storage.addSourceAccount(testSourceAccountWithParams({password}));
            const editedSourceAccount = await storage.editSourceAccount({
                id: existingSourceAccount.id,
                sessionPath: 'newSessionPath',
                accountIdentifier: existingSourceAccount.accountIdentifier
            });
            expect(editedSourceAccount).toEqual({...existingSourceAccount, sessionPath: 'newSessionPath'});
            expect(existsSync(testSourceAccountWithParams().sessionPath)).toBeFalsy();
        });
    });

    describe('Scraper Configuration', () => {
        test('should add new scraper config with new source account', async () => {
            const newScraperConfig = await storage.addScraperConfiguration({source: Source.EPIC_SALES}, testSourceAccountWithParams({password}));

            const checkConfig = await storage.getScraperConfigurationBySource(newScraperConfig.source);
            expect(checkConfig).toEqual(newScraperConfig);

            const savedSourceAccount = await storage.getSourceAccount(checkConfig!.sourceAccountId!);
            expect(savedSourceAccount).toEqual(expect.objectContaining(testSourceAccountWithParams({password})));
        });

        test('should add new scraper config with existing source account ', async () => {
            const newSourceAccount = await storage.addSourceAccount(testSourceAccountWithParams({password}));
            const newScraperConfig = await storage.addScraperConfiguration({source: Source.EPIC_SALES, sourceAccountId: newSourceAccount.id});

            const checkConfig = await storage.getScraperConfigurationBySource(newScraperConfig.source);
            expect(checkConfig).toEqual(newScraperConfig);
        });
        test('should return new in memory scraper config with existing source account and not save it', async () => {
            const newSourceAccount = await storage.addSourceAccount(testSourceAccountWithParams({password}));
            const newScraperConfig = await storage.addScraperConfiguration({source: Source.NINTENDO_SALES, sourceAccountId: newSourceAccount.id});

            const checkConfig = await storage.getScraperConfigurationBySource(Source.NINTENDO_FREE_TO_PLAY, true, false);
            const savedConfigurations = await storage.listScraperConfigurations();
            expect(checkConfig).toEqual({...newScraperConfig, id: `TEMPORARY_MADE_FROM_${newScraperConfig.id}`, source: Source.NINTENDO_FREE_TO_PLAY});
            expect(savedConfigurations).toEqual([newScraperConfig]);
        });

        test('should add new scraper config if config for related source exists and should be used', async () => {
            const newScraperConfig = await storage.addScraperConfiguration({source: Source.STEAM_IMPRESSIONS}, testSourceAccountWithParams({password}));

            const checkConfig = await storage.getScraperConfigurationBySource(Source.STEAM_DISCOUNTS, true);
            expect(checkConfig?.sourceAccountId).toBe(newScraperConfig?.sourceAccountId);
        });

        test(`should return undefined if config for related source exists but shouldn't be used`, async () => {
            await storage.addScraperConfiguration({source: Source.STEAM_IMPRESSIONS}, testSourceAccountWithParams({password}));
            const checkConfig = await storage.getScraperConfigurationBySource(Source.STEAM_DISCOUNTS);
            expect(checkConfig).toBe(undefined);
        });

        test(`should return undefined if config for related source should be used but it doesn't exist`, async () => {
            const checkConfig = await storage.getScraperConfigurationBySource(Source.STEAM_DISCOUNTS, true);
            expect(checkConfig).toBe(undefined);
        });

        test('should add new scraper config with existing source account if both new and existing are specified', async () => {
            const onlyThisSourceAccountWillBeAdded = await storage.addSourceAccount(testSourceAccountWithParams({password}));
            const newScraperConfig = await storage.addScraperConfiguration(
                {source: Source.EPIC_SALES, sourceAccountId: onlyThisSourceAccountWillBeAdded.id},
                testSourceAccountWithParams({password})
            );

            const checkConfig = await storage.getScraperConfigurationBySource(newScraperConfig.source);
            expect(checkConfig).toEqual(newScraperConfig);

            expect(await storage.listSourceAccounts()).toEqual([onlyThisSourceAccountWillBeAdded]);
        });

        test('should add new scraper config with status set to CONFIGURED', async () => {
            const newScraperConfig = await storage.addScraperConfiguration({source: Source.EPIC_SALES}, testSourceAccountWithParams({password}));
            expect(newScraperConfig.status).toEqual(ScraperConfigurationStatus.CONFIGURED);
        });

        test('should fail to add new scraper config with no source account specified', async () => {
            const storage = new ElectronStorageImplementation(newAdapterWithStorage().adapter);
            await expect(storage.addScraperConfiguration({source: Source.EPIC_SALES})).rejects.toThrow('Unable to add Scraper configuration without sourceAccount');
        });

        test.each(EphemeralScraperConfigStates)('should fail to edit scraper config with ephemeral state %s ', async (state) => {
            const storage = new ElectronStorageImplementation(newAdapterWithStorage().adapter);
            const onlyThisSourceAccountWillBeAdded = await storage.addSourceAccount(testSourceAccountWithParams({password}));
            const newScraperConfig = await storage.addScraperConfiguration({source: Source.PLAYSTATION_SALES, sourceAccountId: onlyThisSourceAccountWillBeAdded.id});

            await expect(storage.editScraperConfiguration({...newScraperConfig, status: state})).rejects.toThrow(`${state} state is ephemeral and should not be saved`);
        });

        test('should update status of a scraper config', async () => {
            const source = Source.EPIC_SALES;
            const newStatus = ScraperConfigurationStatus.ERROR;
            const onlyThisSourceAccountWillBeAdded = await storage.addSourceAccount(testSourceAccountWithParams({password}));
            const newScraperConfig = await storage.addScraperConfiguration({source, sourceAccountId: onlyThisSourceAccountWillBeAdded.id});

            await storage.editScraperConfiguration({source, status: newStatus});

            const config = await storage.getScraperConfigurationBySource(source);
            expect(config).toEqual({...newScraperConfig, status: newStatus});
        });

        test('should remove undefined fields when updating status of a scraper config', async () => {
            const source = Source.EPIC_SALES;
            await storage.addScraperConfiguration({
                source,
                sourceAccountId: 'fake',
                errorType: 'UNEXPECTED_ERROR'
            });

            expect(await storage.getScraperConfigurationBySource(source)).toMatchObject({
                errorType: 'UNEXPECTED_ERROR'
            });

            await storage.editScraperConfiguration({source, errorType: undefined});

            const config = await storage.getScraperConfigurationBySource(source);

            expect(config).not.toHaveProperty('errorType');
        });

        test('should not update status if scraper configuration does not exists', async () => {
            await expect(storage.editScraperConfiguration({source: Source.EPIC_SALES, status: ScraperConfigurationStatus.ERROR})).rejects.toThrow(
                `Unable to edit ScraperConfiguration with source ${Source.EPIC_SALES}, because configuration was not found`
            );
            expect(await storage.listScraperConfigurations()).toEqual([]);
        });

        test('deleteSourceAccount should delete source account and linked scraperConfiguration', async () => {
            const sourceAccount = await storage.addSourceAccount(testSourceAccountWithParams());
            await storage.addScraperConfiguration({source: Source.EPIC_SALES, sourceAccountId: sourceAccount.id});

            await storage.deleteSourceAccount(sourceAccount.id);

            expect(await storage.listSourceAccounts()).toEqual([]);
            expect(await storage.listScraperConfigurations()).toEqual([]);
        });
    });

    describe('Daily Cron', () => {
        test('should return undefined if schedule is missing', async () => {
            expect(await storage.getDailyScheduler()).not.toBeDefined();
            expect(memory).toEqual({});
        });

        test('should correctly save schedule', async () => {
            const schedules = [{hours: 21, minutes: 37}];
            expect(await storage.saveDailyScheduler(schedules)).toEqual(schedules);
            expect(memory.dailyScheduler).toEqual(schedules);
        });

        test('should correctly return schedule', async () => {
            const {adapter} = newAdapterWithStorage({
                dailyScheduler: [
                    {
                        hours: 21,
                        minutes: 37
                    }
                ]
            });
            const storage = new ElectronStorageImplementation(adapter);
            const schedules = await storage.getDailyScheduler();
            expect(schedules).toBeInstanceOf(Array);
            expect(schedules).toEqual([{hours: 21, minutes: 37}]);
        });

        test('should correctly return scheduled saved in old format', async () => {
            const {adapter} = newAdapterWithStorage({
                dailyScheduler: {
                    hours: 21,
                    minutes: 37
                }
            });
            const storage = new ElectronStorageImplementation(adapter);
            const schedules = await storage.getDailyScheduler();
            expect(schedules).toBeInstanceOf(Array);
            expect(schedules).toEqual([{hours: 21, minutes: 37}]);
        });
    });
});
