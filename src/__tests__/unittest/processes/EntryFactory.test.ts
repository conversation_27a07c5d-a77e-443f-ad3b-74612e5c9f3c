import {OutputEntry} from '../../../processes/types/entries/OutputEntry';
import {ResultEntry} from '../../../processes/types/entries/ResultEntry';
import {EntryFactory} from '../../../processes/types/EntryFactory';
import {baseEntry} from '../../utils/entries';

describe('EntryFactory', () => {
    it('should create OutputEntry', async () => {
        const json = {
            ...baseEntry,
            type: 'output',
            message: 'test message',
            progress: 50
        } as const;

        const entry = await EntryFactory.create(json);
        expect(entry).toBeInstanceOf(OutputEntry);
        expect(entry.type).toBe('output');
        expect(entry.message).toBe('test message');
        expect(entry.progress).toBe(50);
    });

    it('should create ResultEntry', async () => {
        const json = {
            ...baseEntry,
            type: 'result',
            data: {success: true}
        } as const;

        const entry = await EntryFactory.create(json);
        expect(entry).toBeInstanceOf(ResultEntry);
        expect(entry.type).toBe('result');
        expect(entry.data).toEqual({success: true});
    });

    it('should not validate if type is not set', async () => {
        const json = {
            ...baseEntry,
            type: 'result',
            timestamp: undefined,
            data: {success: true}
        } as const;

        await expect(EntryFactory.create(json)).rejects.toThrow('property timestamp has failed the following constraints: isDateString');
    });

    it('should throw error for unknown type', async () => {
        const json = {
            ...baseEntry,
            type: 'unknown'
        } as any;

        await expect(EntryFactory.create(json)).rejects.toThrow('Unknown entry type: unknown');
    });
});
