import {TraceEntry} from '../../../../processes/types/entries/TraceEntry';
import * as telemetry from '../../../../telemetry/telemetry';
import {baseEntry, baseContext as context} from '../../../utils/entries';
import {testValidationIsCalledWhenEntryIsCreated} from '../../../utils/helpers';
import {mockS2Communication, resetS2Communication} from '../../../utils/s2Mocks';

const json = {...baseEntry, type: 'trace', message: 'Test message'} as const;

describe('TraceEntry', () => {
    beforeEach(() => {
        mockS2Communication();
        jest.spyOn(telemetry, 'trace');
    });

    afterEach(() => {
        jest.restoreAllMocks();
        resetS2Communication();
    });

    testValidationIsCalledWhenEntryIsCreated(TraceEntry, json);

    it('it should be impossible to create output entry with wrong type', async () => {
        const json = {...baseEntry, type: 'wrong-type'} as any;
        await expect(TraceEntry.create(json)).rejects.toThrow('property type has failed the following constraints: equals');
    });

    it('handle should call telemetry.trace, emit event and push to execution output', async () => {
        const traceEntry = await TraceEntry.create(json);

        await traceEntry.handle(context);
        expect(telemetry.trace).toHaveBeenCalledWith(json.message, {
            logLevel: json.logLevel,
            operationId: context.operationId,
            source: json.source,
            additionalData: {
                entryType: 'trace',
                command: 'scrape',
                childProcessPid: undefined
            },
            origin: json.originId
        });
    });
});
