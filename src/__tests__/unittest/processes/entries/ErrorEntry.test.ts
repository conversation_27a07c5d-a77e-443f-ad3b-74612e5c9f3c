import {errorType} from '../../../../configurations/errorType';
import {EntryContext} from '../../../../processes/types/entries/BaseEntry';
import {ErrorEntry} from '../../../../processes/types/entries/ErrorEntry';
import * as telemetry from '../../../../telemetry/telemetry';
import {Deferred} from '../../../../utils/Deferred';
import {baseContext, baseEntry} from '../../../utils/entries';
import {testValidationIsCalledWhenEntryIsCreated} from '../../../utils/helpers';
import {mockS2Communication, resetS2Communication} from '../../../utils/s2Mocks';

const json = {
    ...baseEntry,
    type: 'error',
    errorType: errorType.UNEXPECTED_ERROR,
    data: {
        message: 'test-message',
        stack: 'test-stack',
        additionalErrorData: {key: 'value'}
    }
} as const;

describe('ErrorEntry', () => {
    beforeEach(() => {
        mockS2Communication();
        jest.spyOn(telemetry, 'trace');
    });

    afterEach(() => {
        jest.restoreAllMocks();
        resetS2Communication();
    });

    it('it should be impossible to create error entry with wrong type', async () => {
        const json = {...baseEntry, type: 'wrong-type'} as any;
        await expect(ErrorEntry.create(json)).rejects.toThrow('property type has failed the following constraints: equals');
    });

    it('it should be possible to create error entry with unknown errorType', async () => {
        const jsonWithUnknownErrorType = {
            ...json,
            errorType: 'UNKNOWN_ERROR_NOT_DEFINED_IN_LIB'
        } as any;
        const errorEntry = (await ErrorEntry.create(jsonWithUnknownErrorType)) as ErrorEntry;

        expect(errorEntry.errorType).toBe('UNKNOWN_ERROR_NOT_DEFINED_IN_LIB');
    });

    testValidationIsCalledWhenEntryIsCreated(ErrorEntry, json);

    it('handle should call telemetry.trace, emit event and push to execution output', async () => {
        const errorEntry = await ErrorEntry.create(json);
        const context = {...baseContext, deferredResult: {reject: jest.fn()} as any as Deferred<any>} as any as EntryContext;

        await errorEntry.handle(context);

        expect(context.deferredResult.reject).toHaveBeenCalled();
        expect(telemetry.trace).toHaveBeenCalledWith(json.data.message, {
            logLevel: json.logLevel,
            operationId: context.operationId,
            source: json.source,
            additionalData: {
                entryType: 'error',
                command: 'scrape',
                stack: 'test-stack',
                errorType: json.errorType,
                additionalErrorData: {key: 'value'},
                childProcessPid: undefined
            },
            origin: json.originId
        });
    });
});
