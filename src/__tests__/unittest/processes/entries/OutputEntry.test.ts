import {Emitter} from '../../../../emitter';
import {OutputEntry} from '../../../../processes/types/entries/OutputEntry';
import * as telemetry from '../../../../telemetry/telemetry';
import {baseEntry, baseContext as context} from '../../../utils/entries';
import {mockS2Communication, resetS2Communication} from '../../../utils/s2Mocks';

describe('OutputEntry', () => {
    beforeEach(() => {
        mockS2Communication();
        jest.spyOn(telemetry, 'trace');
        jest.spyOn(Emitter, 'emit').mockImplementation(jest.fn());
    });

    afterEach(() => {
        jest.restoreAllMocks();
        resetS2Communication();
    });

    it('it should be impossible to create output entry with wrong type', async () => {
        const json = {...baseEntry, type: 'wrong-type'} as any;
        await expect(OutputEntry.create(json)).rejects.toThrow('property type has failed the following constraints: equals');
    });

    describe('handle', () => {
        it('should call telemetry.trace, emit event and push to execution output', async () => {
            const json = {...baseEntry, type: 'output', message: 'test message', progress: 44} as const;
            const outputEntry = await OutputEntry.create(json);

            await outputEntry.handle(context);
            expect(telemetry.trace).toHaveBeenCalledWith(json.message, {
                logLevel: json.logLevel,
                operationId: context.operationId,
                source: json.source,
                additionalData: {
                    entryType: 'output',
                    command: 'scrape',
                    progress: json.progress
                },
                origin: json.originId
            });
            expect(Emitter.emit).toHaveBeenCalledWith(json);
            expect(context.execution.output).toContain(json);
        });
    });

    describe('validate', () => {
        it('should pass validation', async () => {
            const json = {...baseEntry, type: 'output', message: 'test message'} as const;
            const outputEntry = await OutputEntry.create(json);
            await outputEntry.validate();
            expect(outputEntry).toBeDefined();
        });

        it('should fail validation with improper metadata', async () => {
            const json = {
                ...baseEntry,
                type: 'output',
                message: 'test message',
                metadata: {...baseEntry.metadata, function_name: 1}
            } as const;
            await expect(OutputEntry.create(json)).rejects.toThrow('property metadata.function_name has failed the following constraints: isString');
        });

        it('should pass validation without metadata', async () => {
            const json = {...baseEntry, type: 'output', message: 'test message', metadata: undefined} as const;
            const outputEntry = await OutputEntry.create(json);
            await outputEntry.validate();
            expect(outputEntry).toBeDefined();
        });
    });
});
