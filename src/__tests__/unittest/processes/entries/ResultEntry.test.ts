import {Emitter} from '../../../../emitter';
import {EntryContext} from '../../../../processes/types/entries/BaseEntry';
import {ResultEntry} from '../../../../processes/types/entries/ResultEntry';
import * as telemetry from '../../../../telemetry/telemetry';
import {Deferred} from '../../../../utils/Deferred';
import {baseContext, baseEntry} from '../../../utils/entries';
import {mockS2Communication, resetS2Communication} from '../../../utils/s2Mocks';

describe('ResultEntry', () => {
    beforeEach(() => {
        mockS2Communication();
        jest.spyOn(telemetry, 'trace');
        jest.spyOn(Emitter, 'emit').mockImplementation(jest.fn());
    });

    afterEach(() => {
        jest.restoreAllMocks();
        resetS2Communication();
    });

    describe('handle', () => {
        it('should send telemetry trace, emit event and push to execution output and resolve deferred result', async () => {
            const json = {...baseEntry, type: 'result', data: {success: true}} as const;
            const resultEntry = await ResultEntry.create(json);
            const context = {...baseContext, deferredResult: {resolve: jest.fn()} as any as Deferred<any>} as any as EntryContext;

            await resultEntry.handle(context);
            expect(telemetry.trace).toHaveBeenCalledWith('result entry', {
                logLevel: json.logLevel,
                operationId: context.operationId,
                source: json.source,
                additionalData: {
                    entryType: 'result',
                    command: 'scrape',
                    result: {success: true}
                },
                origin: json.originId
            });
            expect(Emitter.emit).toHaveBeenCalledWith(json);
            expect(context.execution.output).toContain(json);
            expect(context.deferredResult.resolve).toHaveBeenCalledWith(json.data);
        });
    });
});
