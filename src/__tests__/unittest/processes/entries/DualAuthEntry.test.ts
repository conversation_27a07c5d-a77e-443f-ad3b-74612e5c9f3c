import {Emitter} from '../../../../emitter';
import {DualAuthEntry, DualAuthMethod} from '../../../../processes/types/entries/DualAuthEntry';
import * as telemetry from '../../../../telemetry/telemetry';
import {baseEntry, baseContext as context} from '../../../utils/entries';
import {testValidationIsCalledWhenEntryIsCreated} from '../../../utils/helpers';
import {mockS2Communication, resetS2Communication} from '../../../utils/s2Mocks';

const json = {...baseEntry, type: 'dualAuth', logLevel: 1, attempt: 1, maxAttempts: 3, success: true, portal: 'APPLE', authMethod: DualAuthMethod.EMAIL_CODE} as const;

describe('DualAuthEntry', () => {
    beforeEach(() => {
        mockS2Communication();
        jest.spyOn(telemetry, 'trace').mockImplementation(jest.fn());
        jest.spyOn(Emitter, 'emit').mockImplementation(jest.fn());
    });

    afterEach(() => {
        jest.restoreAllMocks();
        resetS2Communication();
    });

    testValidationIsCalledWhenEntryIsCreated(DualAuthEntry, json);

    it('it should be impossible to create output entry with wrong type', async () => {
        const json = {...baseEntry, type: 'wrong-type'} as any;

        await expect(DualAuthEntry.create(json)).rejects.toThrow('property type has failed the following constraints: equals');
    });

    it('handle should call telemetry.trace, emit event and push to execution output', async () => {
        const dualAuthEntry = await DualAuthEntry.create(json);

        await dualAuthEntry.handle(context);
        expect(telemetry.trace).toHaveBeenCalledWith('dualAuth entry', {
            logLevel: json.logLevel,
            operationId: context.operationId,
            source: json.source,
            additionalData: {
                entryType: 'dualAuth',
                command: 'scrape',
                attempt: json.attempt,
                maxAttempts: json.maxAttempts,
                success: json.success,
                portal: json.portal,
                authMethod: json.authMethod
            },
            origin: json.originId
        });
        expect(Emitter.emit).toHaveBeenCalledWith(json);
        expect(context.execution.output).toContain(json);
    });
});
