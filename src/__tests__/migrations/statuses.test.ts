import {ElectronStorageImplementation} from '../../storage/ElectronStorageImplementation';
import {revertMigration, runMigration} from '../../storage/migrations/statuses';
import {newAdapterWithStorage} from '../utils/helpers';

describe('Status migrations', () => {
    describe('forward migrations', () => {
        const initialState1 = {
            configurations: {
                microsoft_sales: {source: 'microsoft_sales', status: 'NEW'},
                humble_sales: {source: 'humble_sales', status: 'VALID'},
                gog_sales: {source: 'gog_sales', status: 'ERROR'},
                nintendo_sales: {source: 'nintendo_sales', status: 'MANUAL_SESSION_EXPIRED'},
                epic_sales: {source: 'epic_sales', status: 'NEEDS_ACTION'}
            }
        };
        const expectedState1 = {
            microsoft_sales: {source: 'microsoft_sales', status: 'CONFIGURED'},
            humble_sales: {source: 'humble_sales', status: 'CONFIGURED'},
            gog_sales: {source: 'gog_sales', status: 'ERROR'},
            nintendo_sales: {source: 'nintendo_sales', status: 'ERROR', errorType: 'SESSION_EXPIRED'},
            epic_sales: {source: 'epic_sales', status: 'ERROR'}
        };
        const initialState2 = {
            configurations: {
                playstation_sales: {source: 'playstation_sales', status: 'CONFIGURED'},
                microsoft_sales: {source: 'microsoft_sales', status: 'ERROR', typeError: 'SESSION_EXPIRED'},
                humble_sales: {source: 'humble_sales', status: 'ERROR', typeError: 'INCORRECT_CREDENTIALS'},
                gog_sales: {source: 'gog_sales', status: 'ERROR', typeError: 'MISSING_2FA'},
                nintendo_sales: {source: 'nintendo_sales', status: 'ERROR', typeError: 'MISSING_CAPTCHA'},
                epic_sales: {source: 'epic_sales', status: 'ERROR', typeError: 'MISSING_PERMISSIONS'},
                steam_sales: {source: 'steam_sales', status: 'ERROR', typeError: 'IP_BANNED'},
                steam_wishlists: {source: 'steam_wishlists', status: 'ERROR', typeError: 'INCORRECT_2FA'},
                steam_impressions: {source: 'steam_impressions', status: 'ERROR', typeError: 'NO_INTERNET'},
                meta_rift_sales: {source: 'meta_rift_sales', status: 'ERROR', typeError: 'UNEXPECTED_ERROR'},
                meta_quest_sales: {source: 'meta_quest_sales', status: 'ERROR', errorType: 'FOO_BAR_BIZ'}
            }
        };
        test('forward migration', async () => {
            const {adapter, memory} = newAdapterWithStorage(initialState1);
            const storage = new ElectronStorageImplementation(adapter);
            await runMigration(storage);
            expect(memory.configurations).toStrictEqual(expectedState1);
        });

        test('running the same migration multiple times', async () => {
            const {adapter, memory} = newAdapterWithStorage(initialState1);
            const storage = new ElectronStorageImplementation(adapter);
            await runMigration(storage);
            await runMigration(storage);
            await runMigration(storage);
            expect(memory.configurations).toStrictEqual(expectedState1);
        });

        test('running migration on new statuses', async () => {
            const {adapter, memory} = newAdapterWithStorage(initialState2);
            const storage = new ElectronStorageImplementation(adapter);
            await runMigration(storage);
            expect(memory.configurations).toStrictEqual(initialState2.configurations);
        });
    });

    test('test backward migration', async () => {
        const initialState = {
            configurations: {
                microsoft_sales: {source: 'microsoft_sales', status: 'CONFIGURED'},
                humble_sales: {source: 'humble_sales', status: 'ERROR'},
                gog_sales: {source: 'gog_sales', status: 'ERROR', errorType: 'SESSION_EXPIRED'},
                nintendo_sales: {source: 'nintendo_sales', status: 'ERROR', errorType: 'INCORRECT_CREDENTIALS'},
                epic_sales: {source: 'epic_sales', status: 'ERROR', errorType: 'MISSING_2FA'},
                steam_sales: {source: 'steam_sales', status: 'ERROR', errorType: 'FOO_BAR_BIZ'}
            }
        };
        const {adapter, memory} = newAdapterWithStorage(initialState);
        const storage = new ElectronStorageImplementation(adapter);

        await revertMigration(storage);
        expect(memory.configurations).toStrictEqual({
            microsoft_sales: {source: 'microsoft_sales', status: 'VALID'},
            humble_sales: {source: 'humble_sales', status: 'ERROR'},
            gog_sales: {source: 'gog_sales', status: 'MANUAL_SESSION_EXPIRED'},
            nintendo_sales: {source: 'nintendo_sales', status: 'ERROR'},
            epic_sales: {source: 'epic_sales', status: 'ERROR'},
            steam_sales: {source: 'steam_sales', status: 'ERROR'}
        });
    });
});
