#!/bin/bash

echo "{\"timestamp\":\"2024-08-22T13:35:49.881Z\",\"data\":{\"message\":\" You don't have enough permissions to scrape with those credentials. Please check your permissions or use different accounts.\",\"additionalErrorData\":{\"organizationsWithMissingPermissions\":[{\"name\":\"test\",\"id\":\"test\"},{\"name\":\"test2\",\"id\":\"test2\"}]}},\"type\":\"error\",\"errorType\":\"MISSING_PERMISSIONS\",\"logLevel\":\"error\",\"originalError\":{\"name\":\"Error\",\"stack\":\"Error: Missing permissions for ,test,test2 organizations\n    at scrape (/home/<USER>/WebstormProjects/scrapers/dist/src/scrapersV2/steam/games/scrapers/steamDiscounts.js:58:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperProxy.scrape (/home/<USER>/WebstormProjects/scrapers/dist/src/scrapersV2/ScraperProxy.js:45:25)\n    at async descriptor.value (/home/<USER>/WebstormProjects/scrapers/dist/src/scrapersV2/ScraperProxy.js:95:20)\n    at async scrapeCommand (/home/<USER>/WebstormProjects/scrapers/dist/src/cli/cli.js:66:16)\n    at async Object.scrape (/home/<USER>/WebstormProjects/scrapers/dist/src/cli/cli.js:43:20)\n    at async retryAction (/home/<USER>/WebstormProjects/scrapers/dist/src/utils/retryAction.js:19:20)\n    at async /home/<USER>/WebstormProjects/scrapers/dist/src/cli/cli.js:134:32\n    at async main (/home/<USER>/WebstormProjects/scrapers/dist/src/cli/cli.js:100:20)\",\"message\":\"Missing permissions for ,test,test2 organizations\",\"externalTelemetryLogLevel\":\"error\",\"suggestedAction\":\"You don't have enough permissions to scrape with those credentials. Please check your permissions or use different accounts.\",\"errorType\":\"MISSING_PERMISSIONS\",\"shouldRetryScraping\":false,\"additionalErrorData\":{\"organizationsWithMissingPermissions\":[{\"name\":\"test\",\"id\":\"test\"},{\"name\":\"test2\",\"id\":\"test2\"}]}}}"
