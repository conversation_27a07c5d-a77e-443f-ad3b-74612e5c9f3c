import * as <PERSON><PERSON>er<PERSON><PERSON> from '../../api/ScraperApi';
import {ScraperLib} from '../../ScraperLib';
import {Source} from '../../types/Source';
import {generateRandomAuthCode, getFreshLib} from '../utils/helpers';

describe('Send 2fa code for each source', () => {
    let scraperLib: ScraperLib;
    let send2faCodeSpy: jest.SpyInstance;

    beforeEach(async () => {
        scraperLib = await getFreshLib({shouldLogin: false});
        send2faCodeSpy = jest.spyOn(ScraperApi, 'send2faCode').mockImplementation();
    });
    afterEach(async () => {
        await scraperLib.close();
        jest.restoreAllMocks();
    });
    test.each(Object.values(Source))(`Should send 2fa code for %s`, async (source) => {
        const code = generateRandomAuthCode();
        await scraperLib.send2faForSource(source, code);
        expect(send2faCodeSpy).toBeCalledTimes(1);
        expect(send2faCodeSpy).toBeCalledWith(source, code);
    });
});
