import {AxiosInstance} from 'axios';
import {version} from '../../../../package.json';
import {DEFAULT_API_URL, getCliParams, getShadowModeTask, getUploadUrl, logoutFromApi, send2faCode, setupApi} from '../../../api/ScraperApi';
import {ShadowModeScrapeTaskResponse} from '../../../api/types';
import {Command, MISSING_API_TOKEN_ERROR_MSG, Source} from '../../../types';
import {generateRandomAuthCode} from '../../utils/helpers';
import {mockS2Communication, resetS2Communication} from '../../utils/s2Mocks';
import {jwtMock, loginToApiWithMock, mockAxiosInstance} from '../../utils/scraperApiMocks';

let axiosInstance: AxiosInstance;

const checkDefaultStateOfAxiosInstance = () => {
    expect(axiosInstance.defaults.baseURL).toBeUndefined();
    expect(axiosInstance.defaults.headers.common['Authorization']).toEqual(false);
    expect(axiosInstance.defaults.headers.common['x-client-origin']).toBe('electronApp');
    expect(axiosInstance.defaults.headers.common['x-client-version']).toBe(version);
    expect(axiosInstance.defaults.headers.common['Accept']).toBe('application/json, text/plain, */*');
    expect(axiosInstance.defaults.headers.common['User-Agent']).toBe(`ScraperLib/${version}`);
};

describe('ScraperAPI', () => {
    beforeEach(async () => {
        jest.resetAllMocks();
        axiosInstance = await mockAxiosInstance();
        checkDefaultStateOfAxiosInstance();
    });

    afterEach(() => {
        jest.restoreAllMocks();
        resetS2Communication();
    });

    test('setup api, login, logout and reset axiosInstance', async () => {
        setupApi();
        expect(axiosInstance.defaults.baseURL).toBe(DEFAULT_API_URL);
        await loginToApiWithMock();
        expect(axiosInstance.defaults.baseURL).toBe(DEFAULT_API_URL);
        expect(axiosInstance.defaults.headers.common['Authorization']).toBe(`Bearer ${jwtMock}`);
        expect(axiosInstance.defaults.headers.common['x-client-origin']).toBe('electronApp');
        expect(axiosInstance.defaults.headers.common['x-client-version']).toBe(version);
        logoutFromApi();
        expect(axiosInstance.defaults.baseURL).toBe(DEFAULT_API_URL);
        expect(axiosInstance.defaults.headers.common['Authorization']).toBe(false);
    });

    test('get error for api without setup', async () => {
        await expect(async () => await getUploadUrl()).rejects.toThrow(MISSING_API_TOKEN_ERROR_MSG);
    });

    test.each(Object.values(Source))('setup, login and send 2fa code for %s', async (source) => {
        let redisMock = '';
        const authCode = generateRandomAuthCode();
        setupApi();
        await loginToApiWithMock();
        const axiosPostMock = jest.spyOn(axiosInstance, 'post').mockImplementation(async (_url, {authCode} = {}) => {
            redisMock = authCode;
            return;
        });
        await send2faCode(source, authCode);
        expect(axiosPostMock).toBeCalledTimes(1);
        expect(axiosPostMock).toBeCalledWith(`/1/auth-code/${source}`, {authCode});
        expect(redisMock).toBe(authCode);
    });

    test('pass proper params to CLI', async () => {
        const secretToken = 'superSecretToken';
        setupApi(DEFAULT_API_URL, secretToken);
        const {apiToken} = getCliParams();
        expect(apiToken).toEqual(secretToken);
    });

    describe('shadow mode', () => {
        test('getShadowModeTask returns undefined if endpoint returns 204 NO_CONTENT status', async () => {
            jest.spyOn(axiosInstance, 'get').mockImplementation(async () => ({status: 204}));
            setupApi();
            await loginToApiWithMock();
            const task = await getShadowModeTask();
            expect(task).toBeUndefined();
        });

        describe('scrape task', () => {
            const payload: ShadowModeScrapeTaskResponse = {
                command: Command.SCRAPE,
                source: Source.PLAYSTATION_SALES,
                scrapers: '0.69.420',
                chromium: 970485,
                id: '75b1b8d2-7e6f-47fa-b57b-cd90b059e793',
                label: 'initial testing',
                dateRanges: [
                    {
                        dateFrom: '2021-01-15',
                        dateTo: '2021-01-20',
                        daysInRange: 6
                    }
                ]
            };

            test('getShadowModeTask returns a proper task', async () => {
                jest.spyOn(axiosInstance, 'get').mockImplementation(async () => ({data: payload}));
                setupApi();
                await loginToApiWithMock();
                const task = await getShadowModeTask();
                expect(task).toEqual({
                    ...payload,
                    dateRanges: [
                        {
                            dateFrom: new Date('2021-01-15'),
                            dateTo: new Date('2021-01-20'),
                            daysInRange: 6
                        }
                    ]
                });
            });

            test('getShadowModeTask returns undefined if payloads do not pass validation', async () => {
                jest.spyOn(axiosInstance, 'get').mockImplementation(async () => ({data: {id: 'invalid'}}));
                const {scheduleTrace} = mockS2Communication();
                setupApi();
                await loginToApiWithMock();
                const task = await getShadowModeTask();
                expect(task).toBeUndefined();
                expect(scheduleTrace).toHaveBeenCalledWith('Found shadow mode command: {"id":"invalid"} but it is invalid.', expect.any(String), {
                    logLevel: expect.any(Number)
                });
            });

            test('getShadowModeTask returns undefined if payloads do not pass validation - data ranges', async () => {
                jest.spyOn(axiosInstance, 'get').mockImplementation(async () => ({
                    data: {...payload, dateRanges: [{dateFrom: '2021-02-15', dateTo: '02-15-2021'}]}
                }));

                const {scheduleTrace} = mockS2Communication();

                setupApi();
                await loginToApiWithMock();
                const task = await getShadowModeTask();
                expect(task).toBeUndefined();

                expect(scheduleTrace).toHaveBeenCalledWith('Unknown error', 'generic', {logLevel: 3, additionalData: expect.any(Object)});
                //TODO when refactoring shadow mode validation, change this to be more specific
                expect(JSON.stringify((scheduleTrace as jest.Mock).mock.calls[2])).toContain('dateTo must be a valid ISO 8601 date string');
            });
        });
    });
});
