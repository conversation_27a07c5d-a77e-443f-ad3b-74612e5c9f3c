import * as fs from 'fs';
import axios from 'axios';
import {AxiosInstance} from 'axios';
import * as mockFs from 'mock-fs';
import {deleteReport, uploadReport} from '../../../api/reportUpload';
import {DEFAULT_API_URL, setupApi} from '../../../api/ScraperApi';
import {buildReportPath} from '../../../dependencies/BinaryProxy';
import {ScrapeResult} from '../../../processes/types';
import * as telemetry from '../../../telemetry/telemetry';
import {Source} from '../../../types';
import {mockS2Communication, resetS2Communication} from '../../utils/s2Mocks';
import {mockAxiosInstance} from '../../utils/scraperApiMocks';

const mainDir = '/test-dir';
const reportSource = Source.APP_STORE_SALES;
const reportFileName = 'test-report.csv';
const reportFilePath = buildReportPath(mainDir, reportSource, reportFileName);

const mockReport: ScrapeResult = {
    source: reportSource,
    reportFileName: reportFileName,
    startDate: '2023-01-01',
    endDate: '2023-01-08',
    noData: false
} as ScrapeResult;

let scraperApiAxiosInstance: AxiosInstance;

describe('reportUpload functions', () => {
    beforeEach(() => {
        mockS2Communication();
        jest.spyOn(telemetry, 'trace');
        jest.spyOn(telemetry, 'exception');
    });

    afterEach(() => {
        mockFs.restore();
        jest.restoreAllMocks();
        resetS2Communication();
    });

    describe('uploadReport', () => {
        it('should upload a report by doing put request on uploadUrl, which gets from /1/report/upload-url', async () => {
            const mockStream = {
                on: jest.fn().mockImplementation(),
                pipe: jest.fn().mockImplementation()
            };
            jest.spyOn(fs, 'createReadStream').mockReturnValue(mockStream as any);
            jest.spyOn(fs, 'statSync').mockReturnValue({size: 123} as fs.Stats);

            scraperApiAxiosInstance = await mockAxiosInstance();
            setupApi(DEFAULT_API_URL, 'test-token');
            const uploadUrl = 'https://example.com/upload';
            const blobName = 'test-blob-name';
            const getUploadUrlSpy = jest.spyOn(scraperApiAxiosInstance, 'get').mockResolvedValue({data: {url: uploadUrl, blobName}});

            const putSpy = jest.spyOn(axios, 'put').mockResolvedValue({});

            await uploadReport(mainDir, mockReport);

            expect(getUploadUrlSpy).toHaveBeenCalledWith('/1/report/upload-url');
            expect(putSpy).toHaveBeenCalledWith(uploadUrl, expect.anything(), {
                headers: {
                    'x-ms-blob-type': 'BlockBlob',
                    'content-type': 'application/octet-stream',
                    'content-length': 123
                },
                maxContentLength: Infinity,
                maxBodyLength: Infinity
            });
        });
    });

    describe('deleteReport', () => {
        it('should delete a report file and log telemetry', async () => {
            mockFs({
                [reportFilePath]: 'dummy file content'
            });

            expect(fs.existsSync(reportFilePath)).toBe(true);

            await deleteReport(mainDir, mockReport);

            expect(fs.existsSync(reportFilePath)).toBe(false);
            expect(telemetry.trace).toHaveBeenCalledWith('Deleting a report...');
            expect(telemetry.trace).toHaveBeenCalledWith(`Report removed from ${reportFilePath}`);
        });

        it('should handle errors when file deletion fails', async () => {
            mockFs({});

            expect(fs.existsSync(reportFilePath)).toBe(false);

            await expect(deleteReport(mainDir, mockReport)).rejects.toThrow();

            expect(telemetry.trace).toHaveBeenCalledWith('Deleting a report...');
            expect(telemetry.trace).toHaveBeenCalledWith('Failed to delete report');
            expect(telemetry.exception).toHaveBeenCalledWith(expect.any(Error), false, {localFilePath: reportFilePath, report: mockReport});
        });
    });
});
