{"type":"trace","message":"Initializing sentry connection with default options","logLevel":1,"authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.412Z","version":2}
{"message":"Starting example-output command for undefined, attempt: 1/4","logLevel":1,"type":"output","authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.415Z","version":2}
{"message":"Generate example output","logLevel":1,"type":"output","authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.416Z","version":2}
{"type":"trace","message":"Example debug with additional data","data":{"data":[1,2,3]},"logLevel":1,"authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.416Z","version":2}
{"message":"Example info log","logLevel":1,"type":"output","authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.416Z","version":2}
{"type":"trace","message":"Example debug log","logLevel":1,"authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.416Z","version":2}
{"message":"Example warning log","logLevel":2,"type":"output","authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.416Z","version":2}
{"portal":"EPIC","attempt":1,"maxAttempts":5,"type":"dualAuth","logLevel":1,"authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.417Z","version":2}
{"portal":"STEAM","attempt":1,"maxAttempts":1,"authMethod":"MOBILE_APP_APPROVAL","type":"dualAuth","logLevel":1,"authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.417Z","version":2}
{"attempt":1,"authMethod":"MOBILE_APP_APPROVAL","success":true,"type":"dualAuth","logLevel":1,"authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.417Z","version":2}
{"data":{"message":"test - Please try again. If this issue persists, please contact IndieBI support.","additionalErrorData":{"asd":[1,2,4]},"stack":"Error: test\n    at Object.getExampleOutputCommand [as example-output] (/snapshot/scrapers-js/dist/src/cli/cli.js)\n    at Object.target (/snapshot/scrapers-js/dist/src/cli/cli.js)\n    at retryAction (/snapshot/scrapers-js/dist/src/utils/retryAction.js)\n    at main (/snapshot/scrapers-js/dist/src/cli/cli.js)\n    at Object.<anonymous> (/snapshot/scrapers-js/dist/src/scrape.js)\n    at Module._compile (pkg/prelude/bootstrap.js:1930:22)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Module.load (node:internal/modules/cjs/loader:1207:32)\n    at Module._load (node:internal/modules/cjs/loader:1023:12)\n    at Function.runMain (pkg/prelude/bootstrap.js:1983:12)"},"type":"error","errorType":"SESSION_EXPIRED","logLevel":3,"authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.417Z","version":2}
{"data":null,"type":"result","logLevel":1,"authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.418Z","version":2}
{"data":{"id":"example-payload"},"type":"result","logLevel":1,"authenticatedUserId":"not implemented","originId":"Scraper-js-1.0.0","timestamp":"2025-05-27T12:53:15.418Z","version":2}
