{"timestamp": "2025-04-30T12:55:15.707Z", "metadata": {"function_name": "<module>", "line_no": 42, "logger_name": "google.cloud.storage._opentelemetry_tracing", "level_name": "DEBUG"}, "message": "This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.", "logLevel": 1, "type": "trace", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.843Z", "metadata": {"function_name": "configure_sentry", "line_no": 38, "logger_name": "app.sentry", "level_name": "DEBUG"}, "message": "Sentry is disabled", "authenticatedUserId": "N/A", "logLevel": 1, "type": "trace", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.843Z", "metadata": {"function_name": "run", "line_no": 73, "logger_name": "app.util.cli", "level_name": "DEBUG"}, "message": "Attempt 1 of 5", "authenticatedUserId": "N/A", "logLevel": 1, "type": "trace", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.844Z", "metadata": {"function_name": "example_logs", "line_no": 11, "logger_name": "app.util.example_logs", "level_name": "INFO"}, "message": "Example info log", "authenticatedUserId": "N/A", "logLevel": 1, "type": "output", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.844Z", "metadata": {"function_name": "example_logs", "line_no": 12, "logger_name": "app.util.example_logs", "level_name": "DEBUG"}, "message": "Example debug log", "authenticatedUserId": "N/A", "logLevel": 1, "type": "trace", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.844Z", "metadata": {"function_name": "example_logs", "line_no": 13, "logger_name": "app.util.example_logs", "level_name": "WARNING"}, "message": "Example warning log", "authenticatedUserId": "N/A", "logLevel": 2, "type": "output", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.844Z", "metadata": {"function_name": "example_logs", "line_no": 14, "logger_name": "app.util.example_logs", "level_name": "ERROR"}, "message": "Example error log", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.844Z", "metadata": {"function_name": "example_logs", "line_no": 16, "logger_name": "app.util.example_logs", "level_name": "ERROR"}, "message": "Example exception log", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "errorType": "UNEXPECTED_ERROR", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.844Z", "metadata": {"function_name": "example_logs", "line_no": 18, "logger_name": "app.util.example_logs", "level_name": "CRITICAL"}, "message": "Example critical log", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.844Z", "metadata": {"function_name": "example_logs", "line_no": 20, "logger_name": "app.util.example_logs", "level_name": "INFO"}, "message": "Example info with extra param", "authenticatedUserId": "N/A", "logLevel": 1, "type": "output", "originId": "Scraper-py-0.0.0", "extra_param": "example", "version": 2}
{"timestamp": "2025-04-30T12:55:15.845Z", "metadata": {"function_name": "send_exception", "line_no": 23, "logger_name": "app.util.messaging", "level_name": "ERROR"}, "message": "Example exception", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "errorType": "SESSION_EXPIRED", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.845Z", "metadata": {"function_name": "send_exception", "line_no": 38, "logger_name": "app.util.messaging", "level_name": "ERROR"}, "message": "app.core.exceptions.SessionExpiredException: Example exception", "errorType": "SESSION_EXPIRED", "authenticatedUserId": "N/A", "type": "error", "logLevel": 3, "originId": "Scraper-py-0.0.0", "data": {"message": "Example exception", "stack": "  File \"/home/<USER>/projects/indiebi/scrapers-py/app/util/example_logs.py\", line 23, in example_logs\n    raise SessionExpiredException(\"Example exception\")\n"}, "version": 2}
{"timestamp": "2025-04-30T12:55:15.845Z", "metadata": {"function_name": "example_logs", "line_no": 27, "logger_name": "app.util.example_logs", "level_name": "ERROR"}, "message": "error log from except block", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.845Z", "metadata": {"function_name": "example_logs", "line_no": 28, "logger_name": "app.util.example_logs", "level_name": "ERROR"}, "message": "error log from except block, with exec_info=True", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "errorType": "SESSION_EXPIRED", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.846Z", "metadata": {"function_name": "example_logs", "line_no": 29, "logger_name": "app.util.example_logs", "level_name": "ERROR"}, "message": "exception log from except block", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "errorType": "SESSION_EXPIRED", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.846Z", "metadata": {"function_name": "example_logs", "line_no": 30, "logger_name": "app.util.example_logs", "level_name": "ERROR"}, "message": "exception log from except block, with error as extra param", "errorType": "SESSION_EXPIRED", "error": "Example exception", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.846Z", "metadata": {"function_name": "example_logs", "line_no": 34, "logger_name": "app.util.example_logs", "level_name": "ERROR"}, "message": "exception log from except block, with exec_info=True", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "errorType": "SESSION_EXPIRED", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.846Z", "metadata": {"function_name": "example_logs", "line_no": 38, "logger_name": "app.util.example_logs", "level_name": "CRITICAL"}, "message": "critical log from except block", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.846Z", "metadata": {"function_name": "example_logs", "line_no": 39, "logger_name": "app.util.example_logs", "level_name": "CRITICAL"}, "message": "critical log from except block, with error as extra param", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "originId": "Scraper-py-0.0.0", "error": "Example exception", "version": 2}
{"timestamp": "2025-04-30T12:55:15.846Z", "metadata": {"function_name": "example_logs", "line_no": 43, "logger_name": "app.util.example_logs", "level_name": "CRITICAL"}, "message": "critical log from except block, with exec_info=True", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "errorType": "SESSION_EXPIRED", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.846Z", "metadata": {"function_name": "send_exception", "line_no": 23, "logger_name": "app.util.messaging", "level_name": "ERROR"}, "message": "Session expired.", "authenticatedUserId": "N/A", "logLevel": 3, "type": "output", "errorType": "UNEXPECTED_ERROR", "originId": "Scraper-py-0.0.0", "version": 2}
{"timestamp": "2025-04-30T12:55:15.847Z", "metadata": {"function_name": "send_exception", "line_no": 38, "logger_name": "app.util.messaging", "level_name": "ERROR"}, "message": "app.core.exceptions.SessionExpiredException: Session expired.", "errorType": "UNEXPECTED_ERROR", "authenticatedUserId": "N/A", "type": "error", "logLevel": 3, "originId": "Scraper-py-0.0.0", "data": {"message": "Session expired.", "stack": ""}, "version": 2}
{"timestamp": "2025-04-30T12:55:15.847Z", "metadata": {"function_name": "send_result", "line_no": 12, "logger_name": "app.util.messaging", "level_name": "INFO"}, "message": "", "authenticatedUserId": "N/A", "type": "result", "logLevel": 1, "originId": "Scraper-py-0.0.0", "data": {"payload": "example-payload"}, "version": 2}
{"timestamp": "2025-04-30T12:55:15.847Z", "metadata": {"function_name": "main", "line_no": 68, "logger_name": "__main__", "level_name": "DEBUG"}, "message": "Process completed.", "authenticatedUserId": "N/A", "logLevel": 1, "type": "trace", "originId": "Scraper-py-0.0.0", "version": 2}
