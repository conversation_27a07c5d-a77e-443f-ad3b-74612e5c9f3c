import * as fsp from 'node:fs/promises';
import * as path from 'node:path';
import {Emitter} from '../../../emitter';
import {spawnProcess} from '../../../processes/spawnProcess';
import {Command, Source} from '../../../types';

describe('spawnProcess should communicate when:', () => {
    const execDir = '.private/tmp';
    const scriptName = 'binary_mock.sh';
    const execPath = path.join(execDir, scriptName);
    const source = Source.PLAYSTATION_SALES;
    const params = [Command.SCRAPE, `--source=${source}`];
    const timestamp = '2022-08-04 11:08:06,214';
    const resultCommonParams = {
        logLevel: 1,
        timestamp,
        authenticatedUserId: 'fake-auth-user-id',
        source: source,
        originId: 'fake-origin'
    };

    const resultObject = {type: 'result', data: {hello: 'reksio'}, ...resultCommonParams};

    const resultLine = toStandardOutputLine(resultObject);
    // const resultTelemetryTraceAmount = 2;

    //TODO check scraperServiceClient outbox
    // let telemetryTraceSpy;
    let emitterSpy;

    function toStandardOutputLine(input: object): string {
        return `echo "${JSON.stringify(JSON.stringify(input))}"`;
    }

    async function createScript(line: string, resultLine: string): Promise<void> {
        await fsp.writeFile(execPath, `#!/bin/bash\n${line}\n${resultLine}`);
        await fsp.chmod(execPath, 0o777);
    }

    /**
     * The script creation is made on purpose in order to make script maintaining easier (just string lines rather than actuall scripts).
     * We tried to use mockfs however were unable to overcome the EACCESS issue.
     * The scripts were created but despite numerous attempts we were unable to execute them, seems like a limitation of mockfs.
     */
    beforeAll(async () => {
        await fsp.mkdir(execDir, {recursive: true});
    });

    beforeEach(() => {
        emitterSpy = jest.fn();
        Emitter.setEmitter(emitterSpy);
    });

    afterEach(async () => {
        jest.resetAllMocks();
        await fsp.unlink(execPath);
    });

    function emitterWasOnlyCalledWithResultSuccess(emitter) {
        expect(emitter).toHaveBeenCalledTimes(1);
        expect(JSON.parse(emitter.mock.calls[0][0].message)).toStrictEqual(resultObject);
    }

    async function spawnProcessTest() {
        return await spawnProcess<null>(execPath, params, 'fake');
    }

    it('Trace entries should not be visible in output and emitted but should be sent to telemetry', async () => {
        const traceObject = {type: 'trace', message: 'Download finished trace line!', ...resultCommonParams};
        await createScript(toStandardOutputLine(traceObject), resultLine);
        const process = await spawnProcessTest();

        const output = await process.waitForOutput();

        expect(output).toEqual([resultObject]);
        emitterWasOnlyCalledWithResultSuccess(emitterSpy);
        // expect(telemetryTraceSpy).toHaveBeenCalledWith('Download finished trace line!', 1);
    });

    it('result entries should be visible in output and emitted and sent to telemetry trace and event', async () => {
        await createScript('', resultLine);
        const process = await spawnProcessTest();
        const output = await process.waitForOutput();
        expect(output).toEqual([resultObject]);
        emitterWasOnlyCalledWithResultSuccess(emitterSpy);

        // expect(telemetryTraceSpy.mock.calls[telemetryTraceSpy.mock.calls.length - resultTelemetryTraceAmount][0]).toMatch('Finished scrape for playstation_sales (PID:');
    });

    it('event entries should not be visible in output, not be emitted but should be visible in telemetry trace and event', async () => {
        const eventObject = {message: 'EVENT testing line!', data: {sample: 1}, type: 'event', ...resultCommonParams};
        await createScript(toStandardOutputLine(eventObject), resultLine);
        const process = await spawnProcessTest();

        const output = await process.waitForOutput();

        expect(output).toEqual([resultObject]);
        emitterWasOnlyCalledWithResultSuccess(emitterSpy);
        // expect(telemetryTraceSpy).toHaveBeenCalledWith(eventObject.message + JSON.stringify(eventObject.data), 1);
    });

    it('error entries should propagate error, emit it and trace it', async () => {
        const errorObject = {data: {sample: 1}, type: 'error', originalError: JSON.stringify(new Error('fizBazFoo')), ...resultCommonParams};
        const line = toStandardOutputLine(errorObject);
        await createScript(line, '');
        const process = await spawnProcessTest();
        let errorWasThrown = false;
        try {
            await process.result;
            await process.waitForOutput();
        } catch {
            errorWasThrown = true;
        }

        expect(errorWasThrown).toBe(true);
        expect(emitterSpy).toHaveBeenCalledTimes(1);
        expect(JSON.parse(emitterSpy.mock.calls[0][0].message)).toStrictEqual(errorObject);
        // expect(telemetryTraceSpy.mock.calls[telemetryTraceSpy.mock.calls.length - resultTelemetryTraceAmount][0]).toMatch(
        //     'Error while running scrape for playstation_sales (PID:'
        // );
    });

    ['output', 'dualAuth'].forEach((type) => {
        it(`${type} entries should be emitted, visible in output, visible in traces, not visible in events`, async () => {
            const outputObject = {message: 'OUTPUT testing line!', progress: 15, type, ...resultCommonParams};

            const outputLine = toStandardOutputLine(outputObject);
            await createScript(outputLine, resultLine);
            const process = await spawnProcessTest();
            const output = await process.waitForOutput();
            expect(output).toEqual([outputObject, resultObject]);

            expect(emitterSpy).toHaveBeenCalledTimes(2); // output + result
            expect(JSON.parse(emitterSpy.mock.calls[0][0].message)).toStrictEqual(outputObject);
            // expect(telemetryTraceSpy).toHaveBeenCalledWith(outputObject.message, 1);
        });
    });
});
