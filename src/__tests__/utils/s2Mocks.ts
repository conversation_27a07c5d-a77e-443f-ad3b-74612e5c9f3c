import {iScraperServiceClient} from '../../telemetry/scraperService/iScraperServiceClient';
import * as telemetry from '../../telemetry/telemetry';
import {dummyS2Client} from '../../telemetry/telemetry';

export function getMockS2Client(): iScraperServiceClient {
    return {
        close: jest.fn(),
        updateToken: jest.fn(),
        loadOutboxFromFile: jest.fn(),
        startSendingOutbox: jest.fn(),
        scheduleScraperStateChangedEvent: jest.fn(),
        scheduleLoginStateChangedEvent: jest.fn(),
        scheduleTrace: jest.fn(),
        scheduleReportUploadEvent: jest.fn(),
        loadIpInfo: jest.fn()
    };
}

export function mockS2Communication() {
    const client = getMockS2Client();
    telemetry.setS2Client(client);
    return client;
}

export function resetS2Communication() {
    telemetry.setS2Client(dummyS2Client);
}
