import axios, {AxiosInstance} from 'axios';
import * as api from '../../api/ScraperApi';
import {DateRange} from '../../types';

const {loginToApiWithCredentials, resetAxiosInstance} = api;

export const jwtMock = 'token123';
const {create: createAxiosInstance} = axios;

let axiosInstance: AxiosInstance;

export const loginToApiWithMock = async () => {
    const loginMock = jest.spyOn(axiosInstance, 'post');
    loginMock.mockImplementation(async () => ({data: {jwt: jwtMock}}));
    await loginToApiWithCredentials('<EMAIL>', 'pass321');
    loginMock.mockReset();
};

export const mockAxiosInstance = async ({withLogin}: {withLogin?: boolean} = {}) => {
    jest.spyOn(axios, 'create').mockImplementation(() => {
        axiosInstance = createAxiosInstance();
        return axiosInstance;
    });
    resetAxiosInstance();

    if (withLogin) {
        await loginToApiWithMock();
    }

    return axiosInstance;
};

export const mockRequest = (method: 'get' | 'post', data: any) => {
    const spy = jest.spyOn(axiosInstance, method);
    spy.mockImplementation(async () => ({data}));
    return spy;
};

export const mockGetRequest = (data: any) => {
    return mockRequest('get', data);
};

export const mockPostRequest = (data: any) => {
    return mockRequest('post', data);
};

export const mockApiCoverage = (dates: DateRange[]) => {
    const spy = jest.spyOn(api, 'getScrapDates');
    spy.mockImplementation(async () => dates);
    return spy;
};
