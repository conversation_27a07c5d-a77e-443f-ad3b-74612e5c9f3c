import {DateRange} from '../../types';

export type DateRangeWithoutDays = Omit<DateRange, 'daysInRange'>;

const MILLISECONDS_TO_SECONDS = 1000;
const SECONDS_TO_MINUTES = 60;
const MINUTES_TO_HOURS = 60;
const HOURS_TO_DAYS = 24;

const daysDiff = (dateFrom: Date, dateTo: Date): number => {
    const start = new Date(dateFrom).setHours(0, 0, 0, 0);
    const end = new Date(dateTo).setHours(0, 0, 0, 0);
    return Math.floor(Math.abs(start - end) / (MILLISECONDS_TO_SECONDS * SECONDS_TO_MINUTES * MINUTES_TO_HOURS * HOURS_TO_DAYS));
};

const daysInDateRange = ({dateFrom, dateTo}: DateRangeWithoutDays): number => daysDiff(dateFrom, dateTo) + 1;

export const createDateRange = (dateFrom?: string, dateTo?: string): DateRange => {
    const tempRange = {dateFrom: dateFrom ? new Date(dateFrom) : new Date(), dateTo: dateTo ? new Date(dateTo) : new Date()};
    return {...tempRange, daysInRange: daysInDateRange(tempRange)};
};
