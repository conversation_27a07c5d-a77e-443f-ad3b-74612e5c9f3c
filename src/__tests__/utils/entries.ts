import {EntryContext} from '../../processes/types/entries/BaseEntry';
import {Command, ShadowModeTask} from '../../types';
import {Source} from '../../types/Source';

const metadata = {
    function_name: 'test',
    line_no: 1,
    logger_name: 'test',
    level_name: 'test'
};

export const baseEntry = {
    timestamp: '2024-03-20T12:00:00Z',
    logLevel: 1,
    source: Source.APP_STORE_SALES,
    originId: 'test-origin',
    authenticatedUserId: 'test-user',
    metadata,
    version: 2
};

export const baseContext = {
    operationId: 'test-op-id',
    command: Command.SCRAPE,
    execution: {output: []}
} as any as EntryContext;

export const shadowModeScrapeTestTask: ShadowModeTask = {
    source: Source.PLAYSTATION_SALES,
    command: Command.SCRAPE,
    scrapers: 'test-scrapers',
    chromium: 1,
    id: 'test-id',
    label: 'test-label',
    dateRanges: []
};

export const shadowModeLoginTestTask: ShadowModeTask = {
    source: Source.PLAYSTATION_SALES,
    command: Command.LOGIN,
    scrapers: 'test-scrapers',
    chromium: 1,
    id: 'test-id',
    label: 'test-label'
};
