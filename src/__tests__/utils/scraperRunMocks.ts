import * as upload from '../../api/reportUpload';
import {ReportInfo, UploadDTO} from '../../api/types';
import {BinaryProxy} from '../../dependencies/BinaryProxy';
import {DependenciesManager} from '../../dependencies/DependenciesManager';
import * as features from '../../featureFlags/featureFlags';
import {FeatureFlag} from '../../featureFlags/featureFlags';
import {SyncDependenciesJob} from '../../runs/jobs/SyncDependenciesJob';
import {Command, Source} from '../../types';
import {asyncVoidFunc, processResultMock} from './helpers';

export const mockReportInfo: ReportInfo = {
    studio_id: 123,
    original_name: 'report1.csv',
    upload_date: '2020-01-01T00:00:00.000Z',
    file_path_raw: 'test-blob-name',
    date_from: '2020-01-01T00:00:00.000Z',
    date_to: '2020-01-08T00:00:00.000Z',
    source: Source.STEAM_SALES,
    no_data: false,
    state: 'uploaded',
    id: 456
};

export function mockReportUpload() {
    return {
        uploadReportSpy: jest.spyOn(upload, 'uploadReport').mockImplementation(() => Promise.resolve({} as UploadDTO)),
        sendReportUploadInfoSpy: jest.spyOn(upload, 'sendReportUploadInfo').mockResolvedValue(mockReportInfo)
    };
}

export function mockBinaryScrapeCommand(toReturn: any) {
    jest.spyOn(BinaryProxy.prototype, 'run').mockImplementation((cmd) => (cmd === Command.SCRAPE ? processResultMock(toReturn)() : processResultMock(null)()));
}

export function mockDependencies() {
    jest.spyOn(DependenciesManager.prototype, 'getDependencyExecPaths').mockResolvedValue({
        scrapersExecPath: 'dummy',
        chromiumExecPath: 'dummy'
    });

    jest.spyOn(SyncDependenciesJob.prototype, 'execute').mockImplementation(asyncVoidFunc);
}

export function mockFeatureFlags(featureFlags: FeatureFlag[] = []) {
    let mockedFlags = featureFlags;
    jest.spyOn(features, 'getFeatureFlags').mockImplementation(() => mockedFlags);
    jest.spyOn(features, 'setFeatureFlags').mockImplementation((features) => {
        mockedFlags = features;
    });
    jest.spyOn(features, 'isFeatureEnabled').mockImplementation((feature) => mockedFlags.includes(feature));
    return mockedFlags;
}
