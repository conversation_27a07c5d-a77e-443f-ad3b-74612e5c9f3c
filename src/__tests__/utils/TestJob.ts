import {Job} from '../../runs/jobs/Job';

/* eslint-disable @typescript-eslint/no-empty-function */

export class TestJob extends Job {
    started = false;
    finished = false;

    constructor(protected job: (context: any) => Promise<any> = async () => {}, private name?: string) {
        super();
    }

    async execute(context: any): Promise<any> {
        this.started = true;
        const result = await this.job(context);
        this.finished = true;
        return result;
    }

    async onSuccess(): Promise<void> {}

    async onFail(): Promise<void> {}

    async kill(): Promise<void> {}
}
