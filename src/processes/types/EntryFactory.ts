import {BaseEntry, EntryType} from './entries/BaseEntry';
import {DualAuthEntry} from './entries/DualAuthEntry';
import {ErrorEntry} from './entries/ErrorEntry';
import {OutputEntry} from './entries/OutputEntry';
import {ResultEntry} from './entries/ResultEntry';
import {TraceEntry} from './entries/TraceEntry';

export class EntryFactory {
    public static async create<T extends EntryType>(json: Record<string, any> & {type: T}): Promise<EntryMap[T]> {
        const klass = mapping[json.type];
        if (!klass) {
            // telemetry.trace(`Unknown message type: ${JSON.stringify(json)}`, logLevelLevel.Warning, operationId, source);
            throw new Error(`Unknown entry type: ${json.type}. \n\n${JSON.stringify(json, null, 2)}`);
        }

        return (await klass.create(json)) as EntryMap[T];
    }
}

interface EntryClass<T extends EntryType, E extends BaseEntry<T>> {
    create(json: Record<string, any> & {type: T}): Promise<E>;
}

const ENTRY_CLASSES = {
    dualAuth: DualAuthEntry,
    error: ErrorEntry,
    output: OutputEntry,
    result: ResultEntry,
    trace: TraceEntry
} as const;

export type EntryMap = {
    dualAuth: DualAuthEntry;
    error: ErrorEntry;
    output: OutputEntry;
    result: ResultEntry;
    trace: TraceEntry;
};

type EntryClassMap = {
    [K in keyof typeof ENTRY_CLASSES]: EntryClass<any, EntryMap[K]>;
};

const mapping: EntryClassMap = ENTRY_CLASSES as unknown as EntryClassMap;
