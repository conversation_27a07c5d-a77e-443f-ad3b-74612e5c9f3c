import {ErrorType} from '../../configurations/errorType';

/** @deprecated */
export type LogLevel = 'info' | 'warn' | 'error';

export class ScraperLibError extends Error {
    errorType: ErrorType;
    logLevel: LogLevel;
    additionalErrorData?: Record<string, any>;
    originalError: Error | ScraperLibError;
    errorCode: string;

    constructor(errorType: ErrorType, logLevel: LogLevel = 'error', additionalErrorData?: Record<string, any>, errorCode?: string) {
        super(errorType);
        this.errorType = errorType;
        this.logLevel = logLevel;
        this.additionalErrorData = additionalErrorData;
        this.errorCode = 'SCL_' + (errorCode || 'UNSPECIFIED');
    }
}
