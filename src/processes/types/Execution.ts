import {killProcess} from '../processManager';
import {IExecution} from './interfaces';
import {BinaryResult} from './resultTypes';

export class Execution<ResultType extends BinaryResult> implements IExecution<ResultType> {
    public output: Array<unknown> = [];
    public forceKilled = false;
    constructor(public pid: number, public result: Promise<ResultType>) {}

    async waitForOutput(): Promise<Array<unknown>> {
        return this.result.then(() => {
            return this.output;
        });
    }
    exitCode?: number;
    exitSignal?: string;
    public async kill(operationId: string): Promise<void> {
        //TODO pass in constructor?
        this.forceKilled = true;
        await killProcess(this.pid, operationId);
    }
}
