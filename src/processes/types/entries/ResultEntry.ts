import {Equals, IsOptional, ValidateIf, ValidateNested} from 'class-validator';
import {Emitter} from '../../../emitter';
import * as telemetry from '../../../telemetry/telemetry';
import {BinaryResult} from '../resultTypes';
import {BaseEntry, EntryContext} from './BaseEntry';

export class ResultEntry<T extends BinaryResult = BinaryResult> extends BaseEntry<'result'> {
    @Equals('result')
    type: 'result';

    @ValidateIf((_object, value) => value !== undefined)
    @ValidateNested()
    @IsOptional()
    // TODO: add support for nested validation, which requires an instance of a subclass being assigned to the data property
    data: T;

    async handle({command, operationId, childProcessPid, execution, deferredResult}: EntryContext): Promise<void> {
        const additionalData = {entryType: 'result', command, result: this.data, childProcessPid};
        telemetry.trace('result entry', {logLevel: this.logLevel, operationId, source: this.source, additionalData, origin: this.originId});
        Emitter.emit(this.json);
        execution.output.push(this.json);
        deferredResult.resolve(this.json.data);
    }
}
