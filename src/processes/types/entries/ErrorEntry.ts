import {Equals, IsOptional, IsString, <PERSON><PERSON>teIf, ValidateNested} from 'class-validator';
import {ErrorType} from '../../../configurations/errorType';
import {Emitter} from '../../../emitter';
import * as telemetry from '../../../telemetry/telemetry';
import {ScraperLibError} from '../errors';
import {BaseEntry, BaseSubEntry, EntryContext} from './BaseEntry';

class DataSubEntry extends BaseSubEntry {
    @IsString()
    message: string;

    @IsString()
    stack: string;

    @IsOptional()
    additionalErrorData?: Record<string, any>;
}

export class ErrorEntry extends BaseEntry<'error'> {
    @Equals('error')
    type: 'error';

    @ValidateIf((_object, value) => value !== undefined)
    @ValidateNested()
    data: DataSubEntry;

    errorType: ErrorType;

    protected constructor(protected readonly json: Record<string, any> & {type: 'error'}) {
        super(json);
        this.data = new DataSubEntry(json.data);
    }

    async handle({command, operationId, childProcessPid, deferredResult}: EntryContext): Promise<void> {
        const additionalData = {
            entryType: 'error',
            command,
            stack: this.data.stack,
            errorType: this.errorType,
            additionalErrorData: this.data.additionalErrorData,
            childProcessPid
        };
        telemetry.trace(this.data.message, {logLevel: this.logLevel, operationId, source: this.source, additionalData, origin: this.originId});
        telemetry.exception(new Error(this.json.data.message), false, {json: this.json, source: this.source, command, operationId}, operationId, this.source);
        Emitter.emit(this.json);
        deferredResult.reject(new ScraperLibError(this.json.errorType, this.json.logLevel, {fullErrorAsJson: this.json}));
    }
}
