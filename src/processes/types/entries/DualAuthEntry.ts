import {Equals, IsBoolean, IsEnum, Is<PERSON><PERSON>ber, <PERSON>Optional, IsString} from 'class-validator';
import {Emitter} from '../../../emitter';
import * as telemetry from '../../../telemetry/telemetry';
import {BaseEntry, EntryContext} from './BaseEntry';

export enum DualAuthMethod {
    /**
     * User receives a verification code via email
     */
    EMAIL_CODE = 'EMAIL_CODE',

    /**
     * User enters a time-based code from an authenticator app
     * (Google Authenticator, KeePass, Bitwarden, etc.)
     */
    TOTP_CODE = 'TOTP_CODE',

    /**
     * User approves the login attempt through a push notification
     * in their mobile app (i.e. Steam)
     */
    MOBILE_APP_APPROVAL = 'MOBILE_APP_APPROVAL'
}

export class DualAuthEntry extends BaseEntry<'dualAuth'> {
    @Equals('dualAuth')
    type: 'dualAuth';

    @IsString()
    @IsOptional()
    portal: string;

    @IsBoolean()
    @IsOptional()
    success: boolean;

    @IsNumber()
    attempt: number;

    @IsNumber()
    @IsOptional()
    maxAttempts: number;

    @IsEnum(DualAuthMethod)
    @IsOptional()
    authMethod?: DualAuthMethod;

    async handle({operationId, execution, command}: EntryContext): Promise<void> {
        const additionalData = {
            entryType: 'dualAuth',
            command,
            portal: this.portal,
            success: this.success,
            attempt: this.attempt,
            maxAttempts: this.maxAttempts,
            authMethod: this.authMethod
        };
        telemetry.trace('dualAuth entry', {logLevel: this.logLevel, operationId, source: this.source, additionalData, origin: this.originId});
        Emitter.emit(this.json);
        execution.output.push(this.json);
    }
}
