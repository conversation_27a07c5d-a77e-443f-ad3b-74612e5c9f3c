import {Equals, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>teIf} from 'class-validator';
import {ErrorType} from '../../../configurations/errorType';
import {Emitter} from '../../../emitter';
import * as telemetry from '../../../telemetry/telemetry';
import {BaseEntry, EntryContext} from './BaseEntry';

export class OutputEntry extends BaseEntry<'output'> {
    @Equals('output')
    type: 'output';

    @IsNumber()
    @ValidateIf((_object, value) => value !== undefined)
    @Min(0)
    @Max(100)
    progress?: number;

    @IsString()
    message: string;

    @IsOptional()
    errorType: ErrorType;

    @IsString()
    @IsOptional()
    stack: string;

    async handle({operationId, execution, command}: EntryContext): Promise<void> {
        const additionalData: any = {entryType: 'output', command, progress: this.progress};
        if (this?.errorType) {
            additionalData.errorType = this.errorType;
        }
        if (this?.stack) {
            additionalData.stack = this.stack;
        }
        telemetry.trace(this.message, {logLevel: this.logLevel, operationId, source: this.source, additionalData, origin: this.originId}); // TODO: this should be taken from context not from import
        Emitter.emit(this.json);
        execution.output.push(this.json);
    }
}
