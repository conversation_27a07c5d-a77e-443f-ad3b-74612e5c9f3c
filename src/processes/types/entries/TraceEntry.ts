import {Equals, IsOptional, IsString} from 'class-validator';
import {ErrorType} from '../../../configurations/errorType';
import * as telemetry from '../../../telemetry/telemetry';
import {BaseEntry, EntryContext} from './BaseEntry';

export class TraceEntry extends BaseEntry<'trace'> {
    @Equals('trace')
    type: 'trace';

    @IsString()
    message: string;

    @IsOptional()
    errorType: ErrorType;

    @IsString()
    @IsOptional()
    stack: string;

    async handle({operationId, command}: EntryContext): Promise<void> {
        const additionalData: any = {entryType: 'trace', command, childProcessPid: this.json.childProcessPid};
        if (this?.errorType) {
            additionalData.errorType = this.errorType;
        }
        if (this?.stack) {
            additionalData.stack = this.stack;
        }
        telemetry.trace(this.message, {logLevel: this.logLevel, operationId, source: this.source, additionalData, origin: this.originId});
    }
}
