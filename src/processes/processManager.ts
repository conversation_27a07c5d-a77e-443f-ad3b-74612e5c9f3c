import {promisify} from 'util';
import * as kill from 'tree-kill';
import {errorType} from '../configurations/errorType';
import {ScraperServiceLogLevel} from '../telemetry/scraperService/iScraperServiceClient';
import * as telemetry from '../telemetry/telemetry';
import {Command, Source} from '../types';
import {Deferred} from '../utils/Deferred';
import {ScraperLibError} from './types/errors';
import type {IExecution} from './types/interfaces';

type ProcessInfo = {
    source: Source;
    command: Command;
    promise: Deferred<IExecution<any>>;
};

const activeProcesses: Map<number, ProcessInfo> = new Map();

export function registerProcess(pid: number, source: Source, command: Command, promise: ProcessInfo['promise']) {
    activeProcesses.set(pid, {source, command, promise});
}

export async function killAllProcesses() {
    await Promise.all([...activeProcesses.keys()].map((pid) => killProcess(pid, 'kill-all'))); // TODO change this operationId as it is a hack, consider removing it so killProcess could use PID<->operationID mapping
}

export async function killProcess(pid: number, operationId: string) {
    const {source, command, promise} = activeProcesses.get(pid) ?? {};
    telemetry.trace(`Killing ${command} for ${source} (PID:${pid})`, {logLevel: ScraperServiceLogLevel.INFO, operationId, source});
    try {
        await promisify(kill)(pid);
    } catch (error) {
        telemetry.exception(
            error,
            false,
            {
                error,
                pid,
                source,
                command
            },
            operationId,
            source
        );
    }
    promise?.reject(new ScraperLibError(errorType.JOB_STOPPED_BY_USER, 'warn', {message: 'Process killed by ScraperLib'}));
    activeProcesses.delete(pid);
}
