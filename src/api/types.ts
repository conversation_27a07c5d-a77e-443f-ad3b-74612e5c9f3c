import 'reflect-metadata';
import {Type} from 'class-transformer';
import {Equals, IsArray, IsBoolean, IsDateString, IsEnum, IsIn, IsJWT, IsNumber, IsOptional, IsSemVer, IsString, IsUUID, IsUrl, ValidateNested} from 'class-validator';
import {Command, Source} from '../types';

export interface UploadDTO {
    source: Source;
    dateFrom: string;
    dateTo: string;
    originalName: string;
    filePath: string;
    noData?: boolean;
}

export class LoginResponse {
    @IsJWT()
    jwt: string;
}

export class DatesResponse {
    @IsDateString()
    dateFrom: string; // "2016-01-01T00:00:00.000Z"

    @IsDateString()
    dateTo: string; //"2017-05-16T00:00:00.000Z"

    @IsNumber()
    daysInRange: number;
}
export class UploadAuthResponse {
    @IsUrl()
    url: string;

    @IsString()
    blobName: string;
}

export class ReportInfo {
    @IsNumber()
    studio_id: number;
    @IsString()
    original_name: string;
    @IsDateString()
    upload_date: string;
    @IsString()
    file_path_raw: string;
    @IsDateString()
    date_from: string;
    @IsDateString()
    date_to: string;
    @IsEnum(Source)
    source: Source;
    @IsBoolean()
    no_data: boolean;
    @IsString()
    state: string;
    @IsNumber()
    id: number;
}

export interface ReportInfoResponse {
    reports: ReportInfo[];
}

export class BinariesInfo {
    @IsSemVer()
    scrapers: string;

    @IsNumber()
    chromium: number;

    @IsBoolean()
    @IsOptional()
    shadowMode?: boolean;
}

export type BinariesInfoResponse = Record<'default', BinariesInfo> & Partial<Record<Source, BinariesInfo>>;

export class ShadowModeTaskResponse {
    @IsEnum(Source)
    source: Source;

    @IsEnum(Command)
    @IsIn([Command.LOGIN, Command.SCRAPE])
    command: Command;

    @IsString()
    scrapers: string;

    @IsNumber()
    chromium: number;

    @IsUUID()
    id: string;

    @IsString()
    label: string;
}

export class ShadowModeScrapeTaskResponse extends ShadowModeTaskResponse {
    @ValidateNested()
    @IsArray()
    @Type(() => DatesResponse)
    dateRanges: DatesResponse[];

    @Equals(Command.SCRAPE)
    command: Command.SCRAPE;
}

export class ShadowModeLoginTaskResponse extends ShadowModeTaskResponse {
    @Equals(Command.LOGIN)
    command: Command.LOGIN;
}
