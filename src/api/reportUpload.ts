import {createReadStream} from 'fs';
import * as fs from 'fs';
import axios from 'axios';
import {errorType} from '../configurations/errorType';
import {buildReportPath} from '../dependencies/BinaryProxy';
import {ScrapeResult} from '../processes/types';
import {ScraperLibError} from '../processes/types/errors';
import * as telemetry from '../telemetry/telemetry';
import {getUploadUrl, sendReportInfo} from './ScraperApi';
import {ReportInfo, UploadDTO} from './types';

const getReportUploadInfo = (report: ScrapeResult, blobName: string): UploadDTO => ({
    dateFrom: report.startDate,
    dateTo: report.endDate,
    source: report.source,
    noData: report.noData,
    originalName: report.reportFileName,
    filePath: blobName
});
export async function uploadReport(mainDir: string, report: ScrapeResult): Promise<UploadDTO> {
    telemetry.trace('Uploading a report with direct mode...');
    const localFilePath = buildReportPath(mainDir, report.source, report.reportFileName);
    try {
        const {url, blobName} = await getUploadUrl();
        telemetry.trace(`Uploading a report to blobName ${blobName} with url ${url}...`);
        const uploadInfo = getReportUploadInfo(report, blobName);
        telemetry.trace('Upload to azure storage started');
        await uploadToAzureBlob(url, localFilePath);
        telemetry.trace(`Upload to azure storage finished`);
        return uploadInfo;
    } catch (error) {
        telemetry.trace('Failed to upload report');
        telemetry.exception(error, false, {localFilePath, report});
        throw new ScraperLibError(errorType.REPORT_UPLOAD_ERROR);
    }
}

export async function deleteReport(mainDir: string, report: ScrapeResult): Promise<void> {
    telemetry.trace('Deleting a report...');
    const localFilePath = buildReportPath(mainDir, report.source, report.reportFileName);
    try {
        await fs.promises.unlink(localFilePath);
        telemetry.trace(`Report removed from ${localFilePath}`);
    } catch (error) {
        telemetry.trace('Failed to delete report');
        telemetry.exception(error, false, {localFilePath, report});
        throw error;
    }
}

async function uploadToAzureBlob(uploadUrl: string, localFilePath: string): Promise<void> {
    try {
        const stream = createReadStream(localFilePath);
        const {size} = fs.statSync(localFilePath);
        await axios.put(uploadUrl, stream, {
            headers: {
                'x-ms-blob-type': 'BlockBlob',
                'content-type': 'application/octet-stream',
                'content-length': size
            },
            maxContentLength: Infinity,
            maxBodyLength: Infinity
        });
    } catch (error) {
        telemetry.logAndThrowError(error, {uploadUrl, localFilePath});
    }
}

export async function sendReportUploadInfo(uploadInfo: UploadDTO): Promise<ReportInfo> {
    try {
        telemetry.trace('Sending report upload info...');
        const [result] = await sendReportInfo(uploadInfo);
        telemetry.trace(`Report sent, result: ${JSON.stringify(result)}`);
        return result;
    } catch (error) {
        telemetry.logAndThrowError(error, uploadInfo);
    }
}
