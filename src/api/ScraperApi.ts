import axios, {AxiosInstance, AxiosResponse} from 'axios';
import {transformAndValidate} from 'class-transformer-validator';
import {version} from '../../package.json';
import {getChromiumId, getScraperId} from '../dependencies/identifiers';
import {DependencyDetails, DependencyType, SourceDependencies} from '../dependencies/types';
import {Emitter} from '../emitter';
import * as telemetry from '../telemetry/telemetry';
import {Command, DateRange, MISSING_API_TOKEN_ERROR_MSG, ShadowModeTask, Source, eventTypes} from '../types';
import {executableName, getPlatform} from '../utils/platformUtils';
import {retryAction} from '../utils/retryAction';
import {
    BinariesInfoResponse,
    DatesResponse,
    LoginResponse,
    ReportInfo,
    ReportInfoResponse,
    ShadowModeLoginTaskResponse,
    ShadowModeScrapeTaskResponse,
    ShadowModeTaskResponse,
    UploadAuthResponse,
    UploadDTO
} from './types';

export const DEFAULT_API_URL = 'https://scraper-api.indiebi.dev';

let axiosInstance: AxiosInstance;
//Used only in tests
export const resetAxiosInstance = () => {
    axiosInstance = axios.create();
    addAuthenticationHeaders(undefined);
};
resetAxiosInstance();

const apiWithAuth = <T extends (...args: any[]) => any>(func: T): T => {
    return <T>((...args: any[]) => {
        if (!axiosInstance.defaults.headers.common.Authorization) {
            Emitter.emit({type: eventTypes.libError, message: MISSING_API_TOKEN_ERROR_MSG});
            throw new Error(MISSING_API_TOKEN_ERROR_MSG);
        }
        return func(...args);
    });
};

export function addAuthenticationHeaders(authToken?: string) {
    axiosInstance.defaults.headers.common = {
        Authorization: authToken ? `Bearer ${authToken}` : false,
        'x-client-origin': 'electronApp',
        'x-client-version': version,
        Accept: 'application/json, text/plain, */*',
        'User-Agent': `ScraperLib/${version}`
    };
}

function getWithRetry<T>(url: string): Promise<AxiosResponse<T>> {
    return retryAction({
        target: async () => await axiosInstance.get<T>(url),
        delay: 3000,
        backoff: 1,
        maxAttempts: 3,
        label: `getting ${url}`
    });
}

export const setupApi = (url?: string, apiToken?: string) => {
    axiosInstance.defaults.baseURL = url ?? DEFAULT_API_URL;
    addAuthenticationHeaders(apiToken);
};

/** @deprecated */
export const loginToApiWithCredentials = async (email: string, password: string): Promise<string> => {
    const {data} = await axiosInstance.post<LoginResponse>('/1/login', {email, password}).catch((error) => {
        console.log('Login error:', error);
        throw error;
    });
    const authToken = data.jwt;
    addAuthenticationHeaders(authToken);
    return authToken;
};

export const logoutFromApi = () => {
    //TODO for future, this is NOT a real logout
    addAuthenticationHeaders(undefined);
};

export const getScrapDates = apiWithAuth(async (source: Source, includeHotStorage?: boolean): Promise<DateRange[]> => {
    const uri = `/1/scrap-dates/${source}?includeHotStorage=${includeHotStorage ?? false}`;
    const {data} = await getWithRetry<DatesResponse[]>(uri);
    return data.map(({dateFrom, dateTo, daysInRange}) => ({dateFrom: new Date(dateFrom), dateTo: new Date(dateTo), daysInRange}));
});

export const getUploadUrl = apiWithAuth(async (): Promise<UploadAuthResponse> => {
    const {data} = await getWithRetry<UploadAuthResponse>('/1/report/upload-url');
    return data;
});

export const getDependenciesInfo = apiWithAuth(async (): Promise<SourceDependencies> => {
    if (process.env.FORCED_SCRAPER_VERSION && process.env.FORCED_CHROMIUM_REVISION) {
        return {default: createScraperDependencyObject(process.env.FORCED_SCRAPER_VERSION, Number(process.env.FORCED_CHROMIUM_REVISION))};
    }
    const {data} = await getWithRetry<BinariesInfoResponse>('/1/binaries/info');
    const dependencies = {};
    for (const key in data) {
        dependencies[key] = createScraperDependencyObject(data[key].scrapers, data[key].chromium, data[key].shadowMode);
    }
    return dependencies as SourceDependencies;
});

const {platform, arch} = getPlatform();
export const createScraperDependencyObject = (scraperVersion: string, chromeRevision: number, shadowMode = false): DependencyDetails => ({
    id: getScraperId(scraperVersion),
    shadowMode,
    type: DependencyType.SCRAPERS,
    downloadUrl: `https://stscraperbinstorag01db.blob.core.windows.net/prod-scraper-client/bin/${executableName(getScraperId(scraperVersion))}`,
    dependencies: [
        {
            id: getChromiumId(chromeRevision),
            shadowMode,
            type: DependencyType.CHROMIUM,
            downloadUrl: `https://storage.googleapis.com/chromium-browser-snapshots/${arch}/${chromeRevision}/chrome-${platform}.zip`
        }
    ]
});

export const sendReportInfo = apiWithAuth(async (uploadData: UploadDTO): Promise<ReportInfo[]> => {
    const {data} = await axiosInstance.post<ReportInfoResponse>('/1/report/info', uploadData);
    return data.reports;
});

export const send2faCode = apiWithAuth(async (source: Source, authCode: string): Promise<void> => {
    await axiosInstance.post(`/1/auth-code/${source}`, {authCode});
});

export const getCliParams = () => {
    const apiToken = (axiosInstance.defaults.headers.common.Authorization as string) || '';
    return {
        apiToken: apiToken.split('Bearer ')[1],
        apiUrl: axiosInstance.defaults.baseURL
    };
};

export const getShadowModeTask = apiWithAuth(async (): Promise<ShadowModeTask | undefined> => {
    const {status, data} = await getWithRetry<ShadowModeTaskResponse>('/1/shadow-command');
    if (status == 204) {
        return undefined;
    }

    try {
        await transformAndValidate(ShadowModeTaskResponse, data);

        if (data.command === Command.SCRAPE) {
            const scrapeTask = await transformAndValidate(ShadowModeScrapeTaskResponse, data);
            return {
                ...scrapeTask,
                dateRanges: scrapeTask.dateRanges.map(({dateFrom, dateTo, daysInRange}) => ({dateFrom: new Date(dateFrom), dateTo: new Date(dateTo), daysInRange}))
            };
        }

        if (data.command === Command.LOGIN) {
            return await transformAndValidate(ShadowModeLoginTaskResponse, data);
        }
    } catch (e) {
        telemetry.trace(`Found shadow mode command: ${JSON.stringify(data)} but it is invalid.`);
        telemetry.exception(e);
        return undefined;
    }
});

export const getNewSourcesFromAPI = async (): Promise<Source[]> => {
    const {data} = await getWithRetry<Source[]>(`/1/new-sources`);
    return data;
};
