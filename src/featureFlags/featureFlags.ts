import {Source} from '../types';

export type FeatureFlag = 'shadow-mode-enabled' | `${Source}_scrapers` | 'nintendo-discounts-native-login';

const featureFlags: FeatureFlag[] = [];

export function setFeatureFlags(features: FeatureFlag[]) {
    featureFlags.length = 0;
    featureFlags.push(...features);
}

export function getFeatureFlags(): FeatureFlag[] {
    return [...featureFlags];
}

export function isFeatureEnabled(feature: FeatureFlag): boolean {
    return getFeatureFlags().includes(feature);
}
