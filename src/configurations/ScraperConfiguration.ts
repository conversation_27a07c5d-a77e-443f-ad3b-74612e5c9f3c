import {Source} from '../types';
import {ErrorType} from './errorType';
import {SourceAccount} from './SourceAccount';

export type ScraperConfigurationEditParams = Partial<ScraperConfiguration> & Pick<ScraperConfiguration, 'source'>;
export type ScraperConfigurationAddParams = Partial<Omit<ScraperConfiguration, 'id'>> & Pick<ScraperConfiguration, 'source'>;

export enum ScraperConfigurationStatus {
    CONFIGURED = 'CONFIGURED', // displayed as "Scraped" if scraped less than 15 min ago
    RUNNING_SCRAPE = 'RUNNING_SCRAPE', // displayed as "Logged successfully" for 3s after scraper being configured
    SCHEDULED = 'SCHEDULED', // displayed as "Logged successfully" for 3s after scraper being configured
    DISABLED = 'DISABLED', // configured but not active
    PENDING_PERMISSIONS = 'PENDING_PERMISSIONS',
    ERROR = 'ERROR'
}

export const EphemeralScraperConfigStates = [ScraperConfigurationStatus.RUNNING_SCRAPE, ScraperConfigurationStatus.SCHEDULED];

interface BaseScraperConfiguration<T> {
    readonly id: string;
    readonly source: Source;
    readonly skuToIgnore?: string[];
    readonly sourceSideOrganizationsToScrape?: string[];
    sourceAccountId: SourceAccount['id'];

    status: ScraperConfigurationStatus;
    errorType?: ErrorType;
    previousStatus?: ScraperConfigurationStatus;
    previousErrorType?: ErrorType;

    lastSuccessfulScrapeDate?: T;
    createdAt?: T;
}

export type ScraperConfiguration = BaseScraperConfiguration<Date>;
export type RawScraperConfiguration = BaseScraperConfiguration<string>;
