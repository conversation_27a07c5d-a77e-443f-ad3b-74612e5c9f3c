import {v4 as uuidv4} from 'uuid';
import {Storage} from '../storage/Storage';
import {iScraperServiceClient} from '../telemetry/scraperService/iScraperServiceClient';
import {TriggeredBy} from '../telemetry/scraperService/scraperServiceEvents';
import * as telemetry from '../telemetry/telemetry';
import {Source} from '../types';
import {removeFileOrDirectory} from '../utils/fileUtils';
import {getAvailableSources, getRelatedSources} from '../utils/sourceUtils';
import {ScraperConfiguration, ScraperConfigurationAddParams, ScraperConfigurationEditParams, ScraperConfigurationStatus} from './ScraperConfiguration';
import {SourceAccount, SourceAccountAddParams, SourceAccountEditParams} from './SourceAccount';

export class ScraperConfigurationManager {
    constructor(private readonly storage: Storage, private readonly scraperServiceClient: iScraperServiceClient) {}

    public async getScraperConfiguration(source: Source): Promise<ScraperConfiguration | undefined> {
        return this.storage.getScraperConfigurationBySource(source);
    }

    public async getScraperConfigurations(): Promise<ScraperConfiguration[]> {
        const configs = (await this.createMissingScraperConfigurationsBasedOnRelatedSources()) || [];
        return configs.filter((config): config is ScraperConfiguration => !!config);
    }

    public async addSourceAccountAndHandleRelatedConfigs(source: Source, sourceAccount: SourceAccountAddParams): Promise<ScraperConfiguration[]> {
        const {id: sourceAccountId} = await this.storage.addSourceAccount(sourceAccount);
        const changedConfigs: ScraperConfiguration[] = [];
        for (const relatedSource of getRelatedSources(source)) {
            const config = await this.getScraperConfiguration(relatedSource);
            if (!config) {
                changedConfigs.push(
                    await this.storage.addScraperConfiguration({
                        source: relatedSource,
                        sourceAccountId
                    })
                );
            } else {
                changedConfigs.push(
                    await this.storage.editScraperConfiguration({
                        source: relatedSource,
                        sourceAccountId
                    })
                );
            }
        }
        await this.removeAllUnusedSourceAccounts();
        return changedConfigs;
    }

    public async removeAllUnusedSourceAccounts() {
        const configs = await this.storage.listScraperConfigurations();
        const accounts = await this.storage.listSourceAccounts();

        const unusedAccounts = accounts.filter((account) => !configs.some((config) => config.sourceAccountId === account.id));
        await Promise.all(unusedAccounts.map((unusedAccount) => this.deleteSourceAccount(unusedAccount.id)));
    }

    public async createMissingScraperConfigurationsBasedOnRelatedSources(): Promise<Array<ScraperConfiguration | undefined>> {
        return Promise.all(getAvailableSources().map(async (source: Source) => await this.storage.getScraperConfigurationBySource(source, true)));
    }

    public async deleteSourceAccount(sourceAccountId: string): Promise<void> {
        const sourceAccount = await this.storage.getSourceAccount(sourceAccountId);
        if (sourceAccount) {
            await removeFileOrDirectory(sourceAccount.sessionPath);
        }
        return this.storage.deleteSourceAccount(sourceAccountId);
    }

    public async disableScraperConfiguration(source: Source): Promise<ScraperConfiguration> {
        const configuration = await this.storage.editScraperConfiguration({
            source,
            status: ScraperConfigurationStatus.DISABLED
        });
        this.scraperServiceClient.scheduleScraperStateChangedEvent({
            source,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            newState: 'DISABLED',
            operationId: uuidv4()
        });
        return configuration;
    }

    public async enableScraperConfiguration(source: Source): Promise<ScraperConfiguration> {
        const config = await this.getScraperConfigurationBySource(source);
        if (!config) {
            telemetry.logAndThrowError(`Scraper configuration not found for ${source}`, {source});
        }
        const status = config.errorType ? ScraperConfigurationStatus.ERROR : ScraperConfigurationStatus.CONFIGURED;
        const configuration = this.editScraperConfiguration({
            source,
            status: status
        });
        this.scraperServiceClient.scheduleScraperStateChangedEvent({
            source,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            newState: status === ScraperConfigurationStatus.ERROR ? 'FAILED' : 'FINISHED',
            operationId: uuidv4()
        });
        return configuration;
    }

    public async addScraperConfiguration(config: ScraperConfigurationAddParams, sourceAccount?: SourceAccountAddParams): Promise<ScraperConfiguration> {
        return this.storage.addScraperConfiguration(config, sourceAccount);
    }

    public async editScraperConfiguration(scraperConfiguration: ScraperConfigurationEditParams): Promise<ScraperConfiguration> {
        return this.storage.editScraperConfiguration(scraperConfiguration);
    }

    public async addSourceAccount(sourceAccount: SourceAccountAddParams): Promise<SourceAccount> {
        return this.storage.addSourceAccount(sourceAccount);
    }

    getSourceAccount(sourceAccountId: string) {
        return this.storage.getSourceAccount(sourceAccountId);
    }

    async getScraperConfigurationBySource(
        source: Source,
        shouldCreateBasedOnRelatedSources?: boolean,
        saveConfiguration?: boolean
    ): Promise<ScraperConfiguration | undefined> {
        return this.storage.getScraperConfigurationBySource(source, shouldCreateBasedOnRelatedSources, saveConfiguration);
    }

    async editSourceAccount(sourceAccount: SourceAccountEditParams): Promise<SourceAccount> {
        return this.storage.editSourceAccount(sourceAccount);
    }

    async deleteScraperConfiguration(source: Source): Promise<void> {
        return this.storage.deleteScraperConfiguration(source);
    }

    async listSourceAccounts(): Promise<SourceAccount[]> {
        return this.storage.listSourceAccounts();
    }
}
