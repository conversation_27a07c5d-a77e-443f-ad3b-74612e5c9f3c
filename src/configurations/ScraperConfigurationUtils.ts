import {PublicScraperConfiguration} from '../publicInterfaces/PublicScraperConfiguration';
import {Run} from '../runs/Run';
import {RunManager} from '../runs/RunManager';
import {ScrapeRun} from '../runs/ScrapeRun';
import {ensureJsonSerializable} from '../utils/serialization';
import {ScraperConfiguration, ScraperConfigurationStatus} from './ScraperConfiguration';

export function convertToPublicScraperConfiguration(config: ScraperConfiguration, runManager: RunManager): PublicScraperConfiguration {
    const runningRun = runManager.getRunningRunForSource(config.source);
    const queuedRun = runManager.getScheduledRunForSource(config.source);
    const run = runningRun || queuedRun;

    return ensureJsonSerializable(
        {
            id: config.id,
            source: config.source,
            activeRun: createActiveRunInfo(run),
            lastSuccessfulScrapeDate: config.lastSuccessfulScrapeDate,
            status: determineConfigurationStatus(config, runningRun, queuedRun),
            errorType: config.errorType,
            previousStatus: config.previousStatus,
            previousErrorType: config.previousErrorType,
            sourceAccountId: config.sourceAccountId,
            createdAt: config.createdAt
        },
        'Could not serialize configuration object'
    );
}

function createActiveRunInfo(run: Run | undefined): PublicScraperConfiguration['activeRun'] {
    if (!run) {
        return undefined;
    }

    const activeRun = {
        percentageProgress: run.progress,
        startDate: run.startDate
    };

    if (run instanceof ScrapeRun) {
        return {
            ...activeRun,
            numOfDataRangesToScrape: run.dateRanges?.length,
            numberOfDaysToScrape: run.totalItems,
            scrapedDays: run.processedItems
        };
    }

    return activeRun;
}

function determineConfigurationStatus(config: ScraperConfiguration, runningRun: Run | undefined, queuedRun: Run | undefined): ScraperConfigurationStatus {
    if (runningRun) {
        return ScraperConfigurationStatus.RUNNING_SCRAPE;
    }
    if (queuedRun) {
        return ScraperConfigurationStatus.SCHEDULED;
    }
    return config.status;
}
