import {promises as fs} from 'fs';
import * as path from 'path';
import {Storage} from '../storage/Storage';
import {removeFileOrDirectory} from '../utils/fileUtils';
import {buildSessionDir} from '../utils/session';

export default async function removeUnusedSessionFiles(storage: Storage, mainDir: string): Promise<void> {
    const accounts = await storage.listSourceAccounts();
    const sessionPathsFromUsedAccounts = accounts.map((account) => account.sessionPath);

    const sessionDir = buildSessionDir(mainDir);
    const allFilesInSessionDir = await fs.readdir(sessionDir);
    const FilesToBeDeleted = allFilesInSessionDir.filter((file) => !sessionPathsFromUsedAccounts.find((sessionPath) => sessionPath.match(file)));

    await Promise.all(FilesToBeDeleted.map(async (file) => removeFileOrDirectory(path.join(sessionDir, file), false)));
}
