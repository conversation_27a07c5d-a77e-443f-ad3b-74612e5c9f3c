export const errorType = {
    DEPENDENCIES_SYNC_ERROR: 'DEPENDENCIES_SYNC_ERROR',
    DEPENDENCY_EXECUTION_ERROR: 'DEPENDENCY_EXECUTION_ERROR',
    INCORRECT_CREDENTIALS: 'INCORRECT_CREDENTIALS',
    INTERNAL_SCRAPER_LIB_ERROR: 'INTERNAL_SCRAPER_LIB_ERROR',
    JOB_STOPPED_BY_USER: 'JOB_STOPPED_BY_USER',
    REPORT_UPLOAD_ERROR: 'REPORT_UPLOAD_ERROR',
    SESSION_EXPIRED: 'SESSION_EXPIRED',
    UNEXPECTED_ERROR: 'UNEXPECTED_ERROR',

    /***  from scrapers-py ***/
    INCORRECT_2FA: 'INCORRECT_2FA',
    // INCORRECT_CREDENTIALS: 'INCORRECT_CREDENTIALS',
    IP_BANNED: 'IP_BANNED',
    MISSING_2FA: 'MISSING_2FA',
    MISSING_CAPTCHA: 'MISSING_CAPTCHA',
    MISSING_PERMISSIONS: 'MISSING_PERMISSIONS',
    // SESSION_EXPIRED: 'SESSION_EXPIRED',
    TEMPORARY_PORTAL_ISSUE: 'TEMPORARY_PORTAL_ISSUE'
    // UNEXPECTED_ERROR: 'UNEXPECTED_ERROR'

    /***  from scrapers-js ***/

    // + many other dynamically passed from scrapers
} as const;

export type ErrorType = keyof typeof errorType;
