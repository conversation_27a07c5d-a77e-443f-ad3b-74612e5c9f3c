import {Schedule} from './Schedule';

/**
 * Validate value or minutes
 * @param value - value to validate, should be integer number or string in cron format like '4,15'
 * @param maxValue - maximum value possible for the value
 */
const validateTimeRelatedInput = (value: number, maxValue: number): number => {
    if (Math.round(value) !== value) {
        throw new Error('Value should be integer number');
    }
    if (value < 0 || value > maxValue) {
        throw new Error('Value is out of range');
    }
    return value;
};

export const validateSchedule = (schedule: Schedule): Schedule => {
    return {
        hours: validateTimeRelatedInput(schedule.hours, 23),
        minutes: validateTimeRelatedInput(schedule.minutes, 59)
    };
};
