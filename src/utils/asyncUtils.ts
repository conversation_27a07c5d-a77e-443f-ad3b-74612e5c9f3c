/**
 * Allows a change in the execution context in the event loop (waits for the next tick),
 * giving other asynchronous operations a chance to execute.
 *
 * This is particularly useful in tests where you need to ensure that promises
 * or other asynchronous operations have been processed before continuing.
 */
export const nextEventLoopTick = () => new Promise((resolve) => process.nextTick(resolve));

/**
 * Indirect way to check if some promise is still pending or fulfilled/rejected
 */
export const promiseState = (promise: Promise<any>): Promise<'pending' | 'fulfilled' | 'rejected'> => {
    const pending = 'pending';
    return Promise.race([promise, pending]).then(
        (value) => (value === pending ? 'pending' : 'fulfilled'),
        () => 'rejected'
    );
};
