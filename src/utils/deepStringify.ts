import * as stringify from 'json-stringify-safe';

// https://levelup.gitconnected.com/beware-of-using-json-stringify-for-logging-933f18626d51
function replaceNonEnumerableProperties(_key, value) {
    if (value instanceof Error) {
        return Object.getOwnPropertyNames(value).reduce(
            (obj, propName) => {
                obj[propName] = value[propName];
                return obj;
            },
            {name: value.name}
        );
    }
    return value;
}

export function deepStringify(obj: any) {
    return stringify(obj, replaceNonEnumerableProperties);
}
