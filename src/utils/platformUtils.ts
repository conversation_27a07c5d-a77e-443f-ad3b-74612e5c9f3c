export type Platform = 'win' | 'linux' | 'mac';

export type PlatformInfo = {platform: Platform; arch: string};

export const isWindows = () => process.platform === 'win32';
export const isMac = () => process.platform === 'darwin';

export const getPlatform = (): PlatformInfo => {
    return isWindows() ? {platform: 'win', arch: 'Win_x64'} : isMac() ? {platform: 'mac', arch: 'Mac'} : {platform: 'linux', arch: 'Linux_x64'};
};

export const executableName = (name: string) => (isWindows() ? `${name}.exe` : name);
