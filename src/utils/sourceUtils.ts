import {isFeatureEnabled} from '../featureFlags/featureFlags';
import {Source, SourceValues, filterSourcesByPrefix, publicSources, steamGamesSources, steamPoweredSources} from '../types';

export const isSourceEnabled = (source: Source): boolean => {
    return publicSources.includes(source) || isFeatureEnabled(`${source}_scrapers`);
};

export function getAvailableSources() {
    return SourceValues.filter(isSourceEnabled);
}
export function getRelatedSources(source: Source): Source[] {
    const portal = source.split('_')[0];
    let relatedSources: Source[];
    if (portal === 'steam') {
        relatedSources = steamPoweredSources.includes(source) ? steamPoweredSources : steamGamesSources;
    } else {
        relatedSources = filterSourcesByPrefix(`${portal}_`);
    }
    return relatedSources.filter(isSourceEnabled);
}
