import * as path from 'path';
import {v4} from 'uuid';
import {Source} from '../types';
import {copyFile} from './fileUtils';

export const createUUID = () => v4(); // for some reason, I cannot mock this with jest.mock('uuid'), so I wrap it in a function

export const shadowRunSessionFileNamePart = `shadow_run`;
export const buildSessionDir = (mainDir: string) => path.join(mainDir, 'sessions');
export const buildSessionPath = (mainDir: string, source: Source) => path.join(mainDir, 'sessions', `${source}_${createUUID()}_session.json`);

export const copySessionFileForShadowRun = async (sessionPath: string): Promise<string> => {
    const newSessionFileName = `${path.basename(sessionPath, '.json')}_${shadowRunSessionFileNamePart}_${createUUID().substring(0, 8)}.json`;
    const newSessionPath = path.join(path.dirname(sessionPath), newSessionFileName);
    await copyFile(sessionPath, newSessionPath);
    return newSessionPath;
};
