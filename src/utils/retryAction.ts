import {filterSensitiveFields} from '../telemetry/filterSensitiveData';
import * as telemetry from '../telemetry/telemetry';
import {deepStringify} from './deepStringify';
import {pause} from './pause';

export interface RetryActionOptions<T> {
    /** Function to be called repeatedly until it either resolves or maximum number of attempts is reached. */
    target: (attemptCounter: number, maxAttempts: number) => Promise<T>;
    /** Maximum number of attempts to call the target function. */
    maxAttempts: number;
    /** Delay between attempts in milliseconds. */
    delay?: number;
    /** Backoff factor to be applied to the delay between attempts. Defaults to 1 if not provided */
    backoff?: number;
    /** Function that determines whether a retry should be attempted.
     * If not provided, it will allow retries up to maxAttempts.
     * Can be useful to break retrying in case of some specific errors */
    retryCondition?(error: Error): boolean;
    /** Logger object with info and error methods. If not provided, telemetry.trace and telemetry.exception is used. */
    logger?: {
        info: (message: string) => void;
        error: (message: string, additionalContext?: Record<string, any>) => void;
    };
    /** Optional label to be used in logging that determines the action being executed. */
    label?: string;
}

/**
 *  Call an async function repeatedly until it either resolves or maximum number of attempts is reached.
 *  Each exception that causes a retry is logged externally as an information message instead of an error.
 */
export async function retryAction<T>(options: RetryActionOptions<T>): Promise<T> {
    if (!options.logger) {
        options.logger = {
            info: (message: string) => telemetry.trace(message),
            error: (message: string, additionalContext?: Record<string, any>) => telemetry.trace(message, {additionalData: additionalContext})
        };
    }
    let attemptCounter = 0;
    // eslint-disable-next-line no-constant-condition
    while (true) {
        try {
            attemptCounter++;
            if (options.maxAttempts !== 1) {
                options.logger.info(`Starting ${options.label ?? options.target.name}, attempt: ${attemptCounter}/${options.maxAttempts}`);
            }
            return await options.target(attemptCounter, options.maxAttempts);
        } catch (error) {
            if (attemptCounter >= options.maxAttempts || (options.retryCondition && !options.retryCondition(error))) {
                options.logger.info('Aborting retryAction due to maxAttempts or retryCondition');
                throw error;
            }
            options.logger.info(`Retrying action after receiving error`);
            options.logger.error(deepStringify(filterSensitiveFields(error)), options);
            if (options.delay) {
                options.logger.info(`Waiting ${options.delay * (options.backoff || 1) ** attemptCounter}ms before next attempt`);
                await pause(options.delay * (options.backoff || 1) ** attemptCounter);
            }
        }
    }
}
