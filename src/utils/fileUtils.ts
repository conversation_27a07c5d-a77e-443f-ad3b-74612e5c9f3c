import {chmod, createReadStream, createWriteStream, promises as fs} from 'fs';
import * as https from 'https';
import * as path from 'path';
import {URL} from 'url';
import {promisify} from 'util';
import * as chmodr from 'chmodr';
import {Extract} from 'unzipper';
import {v4 as uuidv4} from 'uuid';
import {Emitter} from '../emitter';
import {ScraperServiceLogLevel} from '../telemetry/scraperService/iScraperServiceClient';
import * as telemetry from '../telemetry/telemetry';
import {eventTypes} from '../types';
import {isWindows} from './platformUtils';

function isFileNotExistError(error: any) {
    return error.code === 'ENOENT';
}

export async function createOrOverrideFile(filePath: string): Promise<void> {
    await fs.writeFile(filePath, '', {encoding: 'utf8'});
}

export async function checkFileExists(filePath: string): Promise<boolean> {
    try {
        await fs.access(filePath);
        return true;
    } catch (error) {
        if (isFileNotExistError(error)) {
            return false;
        }
        telemetry.logAndThrowError(error, {filePath});
    }
}

export const ensureDirExists = async (dirPath: string) => {
    try {
        await fs.access(dirPath);
    } catch (error) {
        if (isFileNotExistError(error)) {
            telemetry.trace(`Creating directory ${dirPath}/`);
            return await fs.mkdir(dirPath, {recursive: true});
        }
        telemetry.logAndThrowError(error, {dirPath});
    }
};

export async function removeFileOrDirectory(path: string, throwOnFail = true): Promise<void> {
    try {
        telemetry.trace(`Removing file or directory:  ${path}`);
        await fs.rm(path, {recursive: true, force: true});
        telemetry.trace(`Directory or file: ${path} removed`);
    } catch (error) {
        if (isFileNotExistError(error)) {
            telemetry.trace(`Unable to delete: ${path} no such file or directory. ${error}`);
            return;
        }
        if (throwOnFail) {
            telemetry.logAndThrowError(error);
        } else {
            telemetry.trace(`Unable to delete: ${path} ${error}`, {logLevel: ScraperServiceLogLevel.WARN});
        }
    }
}

export async function downloadFile(url: string, dirPath: string, shouldSkipEmittingEvents = false): Promise<string> {
    const fileName = getFileNameFromURL(url);
    await ensureDirExists(dirPath);
    const filePath = path.join(dirPath, fileName);
    telemetry.trace(`Downloading ${fileName} to ${dirPath}/...`);
    return new Promise((resolve, reject) => {
        let progress = 0;
        let receivedBytes = 0;
        let totalBytes: number | undefined;
        let reportedProgress = 0;
        const request = https.get(url, function (response) {
            if (response.statusCode !== 200) {
                return reject(new Error(`Server responded with ${response.statusCode}: ${response.statusMessage}`));
            }

            const file = createWriteStream(filePath);
            response.pipe(file);
            response.on('data', function (chunk) {
                if (totalBytes) {
                    receivedBytes += chunk.length;
                    progress = Math.floor((receivedBytes / totalBytes) * 100);
                    if (progress > reportedProgress) {
                        if (!shouldSkipEmittingEvents) {
                            Emitter.emit({type: eventTypes.dependencySync, message: `Downloading ${fileName}`, fileName, progress, receivedBytes, totalBytes});
                        }
                        telemetry.trace(`Downloading ${fileName} to ${dirPath}... ${progress}%`);
                    }
                    reportedProgress = progress;
                }
            });
            file.on('finish', async () => {
                file.close();
                if (!isWindows() && !filePath.endsWith('.zip')) {
                    await promisify(chmod)(filePath, 0o755);
                }
                if (!shouldSkipEmittingEvents) {
                    Emitter.emit({type: eventTypes.dependencySync, message: `Download of ${fileName} completed!`, fileName, progress, receivedBytes, totalBytes});
                }
                telemetry.trace(`Download of ${fileName} completed!`);
                return resolve(filePath);
            });
            file.on('error', generateErrorHandler(reject, filePath));
        });

        request.on('response', (response) => {
            totalBytes = parseInt(response.headers['content-length'] || 'NaN');
        });

        request.on('error', generateErrorHandler(reject, filePath));
    });
}

function generateErrorHandler(reject: any, filePath: string) {
    return async (error) => {
        telemetry.exception(error, false, {operation: 'generateErrorHandler', filePath});
        await fs.unlink(filePath); // delete partially downloaded file
        return reject(error);
    };
}

export async function unZipFileAndDelete(filePath: string, destinationPath: string): Promise<void> {
    telemetry.trace(`Unzipping ${filePath} to ${destinationPath}/...`);
    await createReadStream(filePath)
        .pipe(Extract({path: destinationPath}))
        .promise();
    if (!isWindows()) {
        await promisify(chmodr)(destinationPath, 0o755);
        telemetry.trace(`${destinationPath} permissions changed`);
    }
    await fs.unlink(filePath);
    telemetry.trace(`Unzipping ${filePath} to ${destinationPath} completed!`);
}

export const getFileNameFromURL = (url: string) => new URL(url).pathname.split('/').pop()!;

export async function moveFile(sourceFilePath: string, destinationFilePath: string): Promise<void> {
    // This function works even for cases, where file /foo is moved to /foo/bar.
    // Foo directory cannot be created if foo file already exists, therefore we need a temporary file.
    // moving a file up while renaming to a directory name (from /foo/bar to /foo) is not supported yet
    const tmpFilePath = `${sourceFilePath}.tmp${uuidv4().slice(-8)}`;
    await fs.rename(sourceFilePath, tmpFilePath);
    const destinationDir = path.dirname(destinationFilePath);
    await ensureDirExists(destinationDir);
    await fs.rename(tmpFilePath, destinationFilePath);
}

export async function copyFile(sourceFilePath: string, destinationFilePath: string): Promise<void> {
    const destinationDir = path.dirname(destinationFilePath);
    await ensureDirExists(destinationDir);
    await fs.copyFile(sourceFilePath, destinationFilePath);
}
