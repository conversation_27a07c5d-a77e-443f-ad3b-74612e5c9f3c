export const filterItemsFromArray = <T1 extends T2 = any, T2 = T1>(
    sourceItems: T1[],
    itemsToFilter: T2[],
    condition: (sourceItem: T1, itemToFilter: T2) => boolean
): T1[] => {
    return sourceItems.filter((sourceItem) => !itemsToFilter.some((itemToFilter) => condition(sourceItem, itemToFilter)));
};

export const filterByPrefix = <T extends string>(array: T[], prefix: string) => {
    return array.filter((item) => item.startsWith(prefix));
};
