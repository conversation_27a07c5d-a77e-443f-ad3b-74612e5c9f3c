import {promises as fs} from 'fs';
import * as Cryptr from 'cryptr';
import * as _ from 'lodash';
import * as telemetry from '../telemetry/telemetry';

export class EncryptedJsonFile {
    public readonly filePath: string;

    private readonly cryptr: Cryptr;

    constructor(sessionFilePath: string, encryptionKey?: string) {
        this.filePath = sessionFilePath;
        if (encryptionKey) {
            this.cryptr = new Cryptr(encryptionKey);
        }
    }

    public async load(): Promise<any> {
        try {
            let sessionData = await fs.readFile(this.filePath, 'utf8');
            if (this.cryptr) {
                sessionData = this.cryptr.decrypt(sessionData);
            }
            return JSON.parse(sessionData);
        } catch (error) {
            telemetry.exception(error, false, {operation: 'load', filePath: this.filePath, encrypted: !!this.cryptr});
            if (_.get(error, 'code') === 'ENOENT') {
                return {};
            }
            throw error;
        }
    }

    public async save(session: unknown): Promise<void> {
        let sessionData = JSON.stringify(session);
        if (this.cryptr) {
            sessionData = this.cryptr.encrypt(sessionData);
        }
        await fs.writeFile(this.filePath, sessionData);
    }
}
