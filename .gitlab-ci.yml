# You can override the included template(s) by including variable overrides
# SAST customization: https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Secret Detection customization: https://docs.gitlab.com/ee/user/application_security/secret_detection/#customizing-settings
# Dependency Scanning customization: https://docs.gitlab.com/ee/user/application_security/dependency_scanning/#customizing-the-dependency-scanning-settings
# Container Scanning customization: https://docs.gitlab.com/ee/user/application_security/container_scanning/#customizing-the-container-scanning-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#cicd-variable-precedence

default:
    image: crindiebimain.azurecr.io/dpt/ci-cd/scraperlib-node-18.18.2-chromium-970485:prod
    tags:
        - dpt-azure

stages:
    - build
    - tests
    - publish

sast:
    stage: tests

include:
    - template: Security/SAST.gitlab-ci.yml
    - local: "/ci-cd/gitlab/build.yml"
    - local: "/ci-cd/gitlab/test.yml"

publish:
    needs: ["build"]
    stage: publish
    script:
        - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}">.npmrc
        - npm version $CI_COMMIT_TAG --git-tag-version=false --allow-same-version
        - npm publish
    rules:
        - if: $CI_COMMIT_TAG
